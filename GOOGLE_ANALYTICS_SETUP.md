# Google Analytics 集成说明

## 概述
网站已成功集成 Google Analytics 4 (GA4)，跟踪ID: `G-CLVJ0M6D6V`

## 已实现的跟踪功能

### 1. 页面浏览跟踪
- ✅ 自动跟踪所有页面浏览
- ✅ 路由变化时自动发送页面浏览事件
- ✅ 包含页面标题、URL和路径信息

### 2. 计算器事件跟踪
- ✅ **年龄计算事件**: 用户点击计算按钮时触发
  - 事件名称: `calculate_age`
  - 类别: `calculator` 或 `multi_mode_calculator`
  - 标签: 计算器模式 (`standard`, `professional`, `preemie`)
  - 值: 计算出的年龄

- ✅ **结果复制事件**: 用户复制计算结果时触发
  - 事件名称: `copy_result`
  - 类别: `calculator`
  - 标签: 计算器模式

- ✅ **模式切换事件**: 用户切换计算器模式时触发
  - 事件名称: `mode_change`
  - 类别: `calculator`
  - 标签: 新选择的模式

### 3. 技术实现

#### 组件结构
```
src/components/google-analytics.tsx - 主要GA组件
├── GoogleAnalytics - 加载GA脚本和配置
├── trackEvent - 事件跟踪函数
└── trackPageView - 页面浏览跟踪函数
```

#### 集成位置
- `src/app/layout.tsx` - 全局GA组件加载
- `src/components/age-calculator.tsx` - 主计算器事件跟踪
- `src/components/multi-mode-calculator.tsx` - 多模式计算器事件跟踪

## 可跟踪的关键指标

### 用户行为指标
1. **页面浏览量** - 各页面的访问量
2. **计算器使用率** - 用户实际使用计算器的频率
3. **模式偏好** - 用户最常使用的计算器模式
4. **结果复制率** - 用户复制结果的比例

### 转化指标
1. **计算完成率** - 访问者中实际使用计算器的比例
2. **专业模式使用率** - 专业用户的使用情况
3. **页面停留时间** - 用户在各页面的参与度

## 验证方法

### 开发环境验证
```javascript
// 在浏览器控制台检查
console.log('GA loaded:', typeof window.gtag !== 'undefined');
console.log('DataLayer:', window.dataLayer);
```

### 生产环境验证
1. 访问 [Google Analytics](https://analytics.google.com)
2. 选择对应的属性 (G-CLVJ0M6D6V)
3. 查看实时报告确认数据接收

## 隐私合规
- ✅ 不收集个人身份信息
- ✅ 不存储用户输入的生日数据
- ✅ 仅跟踪匿名使用行为
- ✅ 符合GDPR和CCPA要求

## 部署注意事项
- GA脚本使用 `afterInteractive` 策略加载，不影响页面性能
- 所有跟踪代码都有错误处理，不会影响网站功能
- 在开发环境中GA也会正常工作，便于测试

## 未来扩展建议
1. 添加搜索跟踪（如果添加搜索功能）
2. 跟踪外部链接点击
3. 添加自定义维度（如用户类型：专业/个人）
4. 设置转化目标和漏斗分析
