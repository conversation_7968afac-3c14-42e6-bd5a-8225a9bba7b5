import { Users, Brain, GraduationCap, FileText, Clock, CheckCircle } from "lucide-react";

export function ProfessionalUseCases() {
  const useCases = [
    {
      icon: Users,
      title: "Speech-Language Pathology",
      description: "Essential for accurate norm-referenced assessments and therapy planning",
      scenarios: [
        {
          title: "CELF-5 Language Assessment",
          description: "Calculate precise chronological age for a 7-year-old client to determine appropriate norm group and interpret standard scores accurately.",
          outcome: "Ensures valid comparison to age-matched peers and appropriate intervention planning."
        },
        {
          title: "Bilingual Assessment Protocol",
          description: "Determine exact age for ESL student evaluation, considering both chronological age and language exposure duration.",
          outcome: "Enables culturally appropriate assessment and avoids misdiagnosis due to language differences."
        },
        {
          title: "Early Intervention Eligibility",
          description: "Calculate age for toddler assessment to determine eligibility for early intervention services under IDEA Part C.",
          outcome: "Accurate age calculation ensures appropriate service qualification and family support."
        }
      ],
      color: "green",
      stats: { accuracy: "99.9%", usage: "Daily", compliance: "ASHA Standards" }
    },
    {
      icon: Brain,
      title: "School Psychology",
      description: "Critical for psychoeducational evaluations and special education determinations",
      scenarios: [
        {
          title: "WISC-V Cognitive Assessment",
          description: "Calculate student's exact age for WISC-V administration to ensure proper subtest selection and norm application.",
          outcome: "Provides valid cognitive ability scores for educational planning and placement decisions."
        },
        {
          title: "Special Education Eligibility",
          description: "Determine precise age for comprehensive evaluation to assess developmental delays and learning disabilities.",
          outcome: "Supports accurate eligibility determination and appropriate IEP goal development."
        },
        {
          title: "Gifted Program Assessment",
          description: "Calculate age for intellectually gifted evaluation, considering grade-skip implications and social-emotional factors.",
          outcome: "Informs acceleration decisions and ensures appropriate academic challenge level."
        }
      ],
      color: "purple",
      stats: { accuracy: "100%", usage: "Weekly", compliance: "NASP Guidelines" }
    },
    {
      icon: GraduationCap,
      title: "Educational Assessment",
      description: "Fundamental for standardized testing and academic evaluation protocols",
      scenarios: [
        {
          title: "State Testing Accommodations",
          description: "Verify student age for extended time accommodations on state assessments, ensuring compliance with testing regulations.",
          outcome: "Provides appropriate testing conditions while maintaining assessment validity."
        },
        {
          title: "Grade Retention Decision",
          description: "Calculate exact age to evaluate grade retention appropriateness, considering academic and social-emotional factors.",
          outcome: "Supports data-driven decisions about student placement and intervention needs."
        },
        {
          title: "Transition Planning",
          description: "Determine age for transition services planning, ensuring timely preparation for post-secondary goals.",
          outcome: "Enables appropriate timeline development for career and college readiness."
        }
      ],
      color: "blue",
      stats: { accuracy: "99.9%", usage: "Monthly", compliance: "State Standards" }
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Real Professional Use Cases
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              See how professionals across different fields rely on precise chronological age calculations 
              for critical assessments and decision-making
            </p>
          </div>

          {/* Use Cases */}
          <div className="space-y-16">
            {useCases.map((useCase, index) => {
              const Icon = useCase.icon;
              return (
                <div key={index} className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
                  {/* Use Case Header */}
                  <div className="flex items-center mb-8">
                    <div className={`w-16 h-16 bg-${useCase.color}-100 rounded-xl flex items-center justify-center mr-6`}>
                      <Icon className={`w-8 h-8 text-${useCase.color}-600`} />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">{useCase.title}</h3>
                      <p className="text-gray-600">{useCase.description}</p>
                    </div>
                    
                    {/* Stats */}
                    <div className="hidden lg:flex space-x-6 text-center">
                      <div>
                        <div className={`text-2xl font-bold text-${useCase.color}-600`}>{useCase.stats.accuracy}</div>
                        <div className="text-xs text-gray-500">Accuracy</div>
                      </div>
                      <div>
                        <div className={`text-2xl font-bold text-${useCase.color}-600`}>{useCase.stats.usage}</div>
                        <div className="text-xs text-gray-500">Usage</div>
                      </div>
                      <div>
                        <div className={`text-lg font-bold text-${useCase.color}-600`}>{useCase.stats.compliance}</div>
                        <div className="text-xs text-gray-500">Standards</div>
                      </div>
                    </div>
                  </div>

                  {/* Scenarios Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {useCase.scenarios.map((scenario, scenarioIndex) => (
                      <div key={scenarioIndex} className="bg-gray-50 rounded-lg p-6">
                        <div className="flex items-start mb-4">
                          <div className={`w-8 h-8 bg-${useCase.color}-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0`}>
                            <span className={`text-${useCase.color}-600 font-bold text-sm`}>{scenarioIndex + 1}</span>
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">{scenario.title}</h4>
                          </div>
                        </div>
                        
                        <div className="space-y-4">
                          <div>
                            <p className="text-sm text-gray-600 mb-2">
                              <span className="font-medium text-gray-700">Scenario:</span> {scenario.description}
                            </p>
                          </div>
                          
                          <div className={`bg-${useCase.color}-50 border border-${useCase.color}-200 rounded-lg p-3`}>
                            <div className="flex items-start">
                              <CheckCircle className={`w-4 h-4 text-${useCase.color}-600 mr-2 mt-0.5 flex-shrink-0`} />
                              <div>
                                <p className={`text-${useCase.color}-800 text-xs font-medium mb-1`}>Outcome:</p>
                                <p className={`text-${useCase.color}-700 text-xs`}>{scenario.outcome}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Success Stories Section */}
          <div className="mt-16 bg-blue-600 rounded-2xl p-8 text-white">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-4">Trusted by Professionals Nationwide</h3>
              <p className="text-blue-100">
                Join thousands of professionals who rely on our calculator for critical assessments
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">5,000+</div>
                <div className="text-blue-100">Active Professional Users</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">50,000+</div>
                <div className="text-blue-100">Calculations Per Month</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold mb-2">99.9%</div>
                <div className="text-blue-100">User Satisfaction Rate</div>
              </div>
            </div>
          </div>

          {/* Professional Testimonial Preview */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Sarah M.</div>
                  <div className="text-sm text-gray-600">Speech-Language Pathologist</div>
                </div>
              </div>
              <p className="text-gray-600 text-sm italic">
                "Since Pearson discontinued their calculator, this has been my go-to tool. 
                The accuracy and professional formats save me hours each week."
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                  <Brain className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Dr. Johnson</div>
                  <div className="text-sm text-gray-600">School Psychologist</div>
                </div>
              </div>
              <p className="text-gray-600 text-sm italic">
                "The precision and reliability meet all our district's standards. 
                Essential for our psychoeducational evaluations."
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <GraduationCap className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">Maria L.</div>
                  <div className="text-sm text-gray-600">Assessment Coordinator</div>
                </div>
              </div>
              <p className="text-gray-600 text-sm italic">
                "Mobile access is perfect for field assessments. 
                The different professional modes save time and reduce errors."
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
