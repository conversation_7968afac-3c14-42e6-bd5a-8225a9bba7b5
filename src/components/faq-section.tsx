import {
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";

export function FaqSection() {
  return (
    <section id="faq" className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl mb-6 shadow-lg">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Get expert answers to common questions about chronological age calculation and our professional-grade calculator
            </p>
          </div>

          <Accordion type="single" collapsible className="space-y-6">
            <AccordionItem value="item-1" className="group bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
              <AccordionTrigger className="px-8 py-7 text-left hover:no-underline hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300">
                <div className="flex items-center w-full">
                  <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-xl">Q</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
                      Is this calculator accurate?
                    </h3>
                    <p className="text-sm text-gray-500">Professional-grade precision for all calculations</p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-8 pb-8">
                <div className="ml-19">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 mb-6 border border-blue-100">
                    <div className="flex items-start mb-6">
                      <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-2">99.9% Accuracy Guaranteed</h4>
                        <p className="text-gray-700 text-lg leading-relaxed">
                          Our calculator uses advanced date arithmetic algorithms that handle all edge cases and calendar complexities with professional-grade precision.
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="bg-white rounded-xl p-5 shadow-sm border border-green-100">
                        <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mb-4">
                          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-2">Leap Years</h5>
                        <p className="text-sm text-gray-600">Handles February 29th and century rules perfectly</p>
                      </div>
                      <div className="bg-white rounded-xl p-5 shadow-sm border border-blue-100">
                        <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mb-4">
                          <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-2">Month Lengths</h5>
                        <p className="text-sm text-gray-600">Accounts for 28, 29, 30, and 31-day months</p>
                      </div>
                      <div className="bg-white rounded-xl p-5 shadow-sm border border-purple-100">
                        <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center mb-4">
                          <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-2">Edge Cases</h5>
                        <p className="text-sm text-gray-600">Month-end dates and cross-year calculations</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center p-5 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border-l-4 border-green-400">
                    <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-4">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <p className="text-green-800 font-semibold text-lg">
                      Trusted by healthcare professionals and educational institutions worldwide
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-2" className="group bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
              <AccordionTrigger className="px-8 py-7 text-left hover:no-underline hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 transition-all duration-300">
                <div className="flex items-center w-full">
                  <div className="w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mr-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-xl">Q</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
                      How is chronological age calculated?
                    </h3>
                    <p className="text-sm text-gray-500">Step-by-step calculation methodology</p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-8 pb-8">
                <div className="ml-19">
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-8 mb-6 border border-green-100">
                    <p className="text-gray-800 text-lg leading-relaxed mb-6">
                      Chronological age calculation involves precise time measurement from birth date to assessment date, considering all calendar complexities:
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                      <div className="bg-white rounded-xl p-6 shadow-sm border border-blue-100">
                        <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <span className="text-white font-bold text-lg">1</span>
                        </div>
                        <h4 className="font-bold text-gray-900 mb-3">Complete Years</h4>
                        <p className="text-gray-600 text-sm leading-relaxed">
                          Count full years from birth year to current year, adjusting if birthday hasn't occurred yet this year
                        </p>
                      </div>
                      <div className="bg-white rounded-xl p-6 shadow-sm border border-green-100">
                        <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <span className="text-white font-bold text-lg">2</span>
                        </div>
                        <h4 className="font-bold text-gray-900 mb-3">Complete Months</h4>
                        <p className="text-gray-600 text-sm leading-relaxed">
                          Calculate remaining months after the last birthday, accounting for varying month lengths
                        </p>
                      </div>
                      <div className="bg-white rounded-xl p-6 shadow-sm border border-purple-100">
                        <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <span className="text-white font-bold text-lg">3</span>
                        </div>
                        <h4 className="font-bold text-gray-900 mb-3">Remaining Days</h4>
                        <p className="text-gray-600 text-sm leading-relaxed">
                          Count days after the last complete month, handling leap years and month-end variations
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h4 className="font-bold text-blue-900">Automatic Complexity Handling</h4>
                    </div>
                    <p className="text-blue-800 leading-relaxed">
                      Our calculator automatically manages leap years, varying month lengths, and edge cases like month-end birthdays, ensuring accurate results every time.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-3" className="group bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
              <AccordionTrigger className="px-8 py-7 text-left hover:no-underline hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 transition-all duration-300">
                <div className="flex items-center w-full">
                  <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mr-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-xl">Q</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
                      Why do I need to know my exact age?
                    </h3>
                    <p className="text-sm text-gray-500">Professional applications requiring precision</p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-8 pb-8">
                <div className="ml-19">
                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-8 mb-6 border border-purple-100">
                    <p className="text-gray-800 text-lg leading-relaxed mb-6">
                      While knowing your age in years is sufficient for most purposes, precise age calculation is critical in several professional contexts:
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-white rounded-xl p-6 shadow-sm border border-red-100">
                        <div className="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                          </svg>
                        </div>
                        <h4 className="font-bold text-gray-900 mb-3">Medical Applications</h4>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• Pediatric growth chart assessments</li>
                          <li>• Age-specific medication dosing</li>
                          <li>• Developmental milestone tracking</li>
                          <li>• Clinical research eligibility</li>
                        </ul>
                      </div>

                      <div className="bg-white rounded-xl p-6 shadow-sm border border-blue-100">
                        <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                          </svg>
                        </div>
                        <h4 className="font-bold text-gray-900 mb-3">Educational Use</h4>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• School admission requirements</li>
                          <li>• Standardized test eligibility</li>
                          <li>• Special education assessments</li>
                          <li>• Academic research studies</li>
                        </ul>
                      </div>

                      <div className="bg-white rounded-xl p-6 shadow-sm border border-green-100">
                        <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <h4 className="font-bold text-gray-900 mb-3">Legal & Administrative</h4>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• Legal age verification</li>
                          <li>• Insurance calculations</li>
                          <li>• Court proceedings</li>
                          <li>• Immigration documentation</li>
                        </ul>
                      </div>

                      <div className="bg-white rounded-xl p-6 shadow-sm border border-yellow-100">
                        <div className="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>
                        </div>
                        <h4 className="font-bold text-gray-900 mb-3">Research & Analytics</h4>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• Clinical trial eligibility</li>
                          <li>• Demographic analysis</li>
                          <li>• Longitudinal studies</li>
                          <li>• Statistical research</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl p-6 border border-amber-200">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 bg-amber-500 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                      </div>
                      <h4 className="font-bold text-amber-900">Precision Matters</h4>
                    </div>
                    <p className="text-amber-800 leading-relaxed">
                      In professional contexts, even small differences in age calculation can significantly impact outcomes, eligibility, and decision-making processes.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-4" className="group bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
              <AccordionTrigger className="px-8 py-7 text-left hover:no-underline hover:bg-gradient-to-r hover:from-indigo-50 hover:to-blue-50 transition-all duration-300">
                <div className="flex items-center w-full">
                  <div className="w-14 h-14 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl flex items-center justify-center mr-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-xl">Q</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
                      Can I use this calculator for professional purposes?
                    </h3>
                    <p className="text-sm text-gray-500">Professional-grade accuracy and compliance</p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-8 pb-8">
                <div className="ml-19">
                  <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-2xl p-8 mb-6 border border-indigo-100">
                    <div className="flex items-start mb-6">
                      <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-2">Absolutely Professional-Ready</h4>
                        <p className="text-gray-700 text-lg leading-relaxed">
                          Our calculator meets the precision standards required for medical assessments, educational evaluations, and professional documentation.
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div className="bg-white rounded-xl p-6 shadow-sm border border-green-100">
                        <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mb-4">
                          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-2">Professional Standards</h5>
                        <p className="text-sm text-gray-600">Meets ISO 8601 date standards and medical precision requirements</p>
                      </div>
                      <div className="bg-white rounded-xl p-6 shadow-sm border border-blue-100">
                        <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mb-4">
                          <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-2">Multiple Modes</h5>
                        <p className="text-sm text-gray-600">Standard, Professional, and Preemie modes for different use cases</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl p-6 border border-yellow-200">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                      </div>
                      <h4 className="font-bold text-yellow-900">Professional Recommendation</h4>
                    </div>
                    <p className="text-yellow-800 leading-relaxed">
                      While our calculator provides professional-grade accuracy, always verify calculations according to your specific assessment protocols and institutional guidelines for critical applications.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-5" className="group bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
              <AccordionTrigger className="px-8 py-7 text-left hover:no-underline hover:bg-gradient-to-r hover:from-emerald-50 hover:to-green-50 transition-all duration-300">
                <div className="flex items-center w-full">
                  <div className="w-14 h-14 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mr-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-xl">Q</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
                      Is my personal information safe?
                    </h3>
                    <p className="text-sm text-gray-500">Complete privacy and data protection</p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-8 pb-8">
                <div className="ml-19">
                  <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl p-8 mb-6 border border-emerald-100">
                    <div className="flex items-start mb-6">
                      <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-2">100% Private & Secure</h4>
                        <p className="text-gray-700 text-lg leading-relaxed">
                          All calculations are performed locally in your browser. Zero data transmission, zero storage, complete privacy.
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="bg-white rounded-xl p-5 shadow-sm border border-green-100">
                        <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mb-4">
                          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-2">Local Processing</h5>
                        <p className="text-sm text-gray-600">All calculations happen in your browser only</p>
                      </div>
                      <div className="bg-white rounded-xl p-5 shadow-sm border border-blue-100">
                        <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mb-4">
                          <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-2">No Data Storage</h5>
                        <p className="text-sm text-gray-600">We never store your personal information</p>
                      </div>
                      <div className="bg-white rounded-xl p-5 shadow-sm border border-purple-100">
                        <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center mb-4">
                          <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-2">No Transmission</h5>
                        <p className="text-sm text-gray-600">Data never leaves your device</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <h4 className="font-bold text-green-900">Privacy Guarantee</h4>
                    </div>
                    <p className="text-green-800 leading-relaxed">
                      Your birth date and calculation results remain completely private and are never sent to our servers. We have no access to your personal information.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-6" className="group bg-white rounded-2xl shadow-lg border-0 overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
              <AccordionTrigger className="px-8 py-7 text-left hover:no-underline hover:bg-gradient-to-r hover:from-orange-50 hover:to-red-50 transition-all duration-300">
                <div className="flex items-center w-full">
                  <div className="w-14 h-14 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mr-5 shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <span className="text-white font-bold text-xl">Q</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
                      What about leap years and different month lengths?
                    </h3>
                    <p className="text-sm text-gray-500">Advanced calendar complexity handling</p>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-8 pb-8">
                <div className="ml-19">
                  <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-8 mb-6 border border-orange-100">
                    <div className="flex items-start mb-6">
                      <div className="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-2">Automatic Calendar Complexity Handling</h4>
                        <p className="text-gray-700 text-lg leading-relaxed">
                          Our calculator automatically manages all calendar complexities, ensuring accurate calculations regardless of date variations.
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div className="bg-white rounded-xl p-6 shadow-sm border border-blue-100">
                        <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-3">Leap Year Rules</h5>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• Every 4 years (2020, 2024, 2028...)</li>
                          <li>• Century exceptions (1900 not leap)</li>
                          <li>• 400-year rule (2000 is leap)</li>
                          <li>• February 29th handling</li>
                        </ul>
                      </div>

                      <div className="bg-white rounded-xl p-6 shadow-sm border border-green-100">
                        <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-3">Month Variations</h5>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• February: 28 or 29 days</li>
                          <li>• April, June, Sept, Nov: 30 days</li>
                          <li>• Jan, Mar, May, July, Aug, Oct, Dec: 31 days</li>
                          <li>• Month-end date handling</li>
                        </ul>
                      </div>

                      <div className="bg-white rounded-xl p-6 shadow-sm border border-purple-100">
                        <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-3">Edge Cases</h5>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• Born on January 31st</li>
                          <li>• Cross-year calculations</li>
                          <li>• Future date validation</li>
                          <li>• Timezone considerations</li>
                        </ul>
                      </div>

                      <div className="bg-white rounded-xl p-6 shadow-sm border border-indigo-100">
                        <div className="w-12 h-12 bg-indigo-500 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9" />
                          </svg>
                        </div>
                        <h5 className="font-bold text-gray-900 mb-3">Standards</h5>
                        <ul className="text-sm text-gray-600 space-y-2">
                          <li>• ISO 8601 compliance</li>
                          <li>• Gregorian calendar</li>
                          <li>• Medical standards</li>
                          <li>• International compatibility</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                    <div className="flex items-center mb-4">
                      <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h4 className="font-bold text-blue-900">Universal Accuracy</h4>
                    </div>
                    <p className="text-blue-800 leading-relaxed">
                      This ensures accurate calculations regardless of when you were born or what date you're calculating to, handling all calendar complexities automatically.
                    </p>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </section>
  );
}
