"use client";

import { useState, useEffect } from "react";
import { Calculator, Copy, Download, <PERSON>, Brain, User, Baby, HelpCircle } from "lucide-react";
import { calculateChronologicalAge, formatAgeForProfessional, type AgeCalculationResult } from "@/lib/age-calculator";
import { trackEvent } from "@/components/google-analytics";

type CalculatorMode = 'professional' | 'slp' | 'psychology' | 'preemie' | 'general';

export function MultiModeCalculator() {
  const [mode, setMode] = useState<CalculatorMode>('professional');
  const [birthDate, setBirthDate] = useState('');
  const [testDate, setTestDate] = useState("");
  const [assessmentTool, setAssessmentTool] = useState('');
  const [result, setResult] = useState<AgeCalculationResult | null>(null);
  const [showModeDetails, setShowModeDetails] = useState<string | null>(null);

  const modes = {
    professional: {
      title: "Professional Assessment",
      icon: Calculator,
      description: "For standardized assessments and evaluations",
      color: "blue",
      features: ["Test date flexibility", "Y;M;D format", "Report-ready output"]
    },
    slp: {
      title: "Speech Therapy",
      icon: Users,
      description: "For SLP evaluations and therapy planning",
      color: "green",
      features: ["CELF-5, PPVT-5 ready", "Developmental milestones", "Client tracking"]
    },
    psychology: {
      title: "School Psychology",
      icon: Brain,
      description: "For psychoeducational assessments",
      color: "purple",
      features: ["IEP evaluations", "Grade-age analysis", "Special education"]
    },
    preemie: {
      title: "Preemie Calculator",
      icon: Baby,
      description: "For premature babies with corrected age",
      color: "pink",
      features: ["Corrected age calculation", "Gestational age input", "NICU assessments"]
    },
    general: {
      title: "General Use",
      icon: User,
      description: "For general age calculations",
      color: "gray",
      features: ["Multiple formats", "Fun statistics", "Social sharing"]
    }
  };

  // 在客户端设置当前日期，避免hydration错误
  useEffect(() => {
    setTestDate(new Date().toISOString().split('T')[0]);
  }, []);

  // 点击外部关闭详情
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showModeDetails && !(event.target as Element).closest('.mode-details-container')) {
        setShowModeDetails(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showModeDetails]);

  const calculateAge = () => {
    if (!birthDate || !testDate) {
      alert("Please enter both birth date and assessment date");
      return;
    }

    // Performance monitoring
    const startTime = performance.now();

    const result = calculateChronologicalAge(birthDate, testDate);

    const endTime = performance.now();
    const calculationTime = endTime - startTime;

    // Log performance in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Age calculation completed in ${calculationTime.toFixed(2)}ms`);
    }

    if (!result.isValid) {
      alert(result.errorMessage || "Invalid calculation");
      return;
    }

    setResult(result);

    // Track calculation event
    trackEvent('calculate_age', 'multi_mode_calculator', mode, result.years);
  };

  const formatResult = () => {
    if (!result || !result.isValid) return '';

    switch (mode) {
      case 'professional':
        return formatAgeForProfessional(result, 'standard');
      case 'slp':
        return formatAgeForProfessional(result, 'slp');
      case 'psychology':
        return formatAgeForProfessional(result, 'psychology');
      case 'general':
        return formatAgeForProfessional(result, 'detailed');
      default:
        return formatAgeForProfessional(result, 'detailed');
    }
  };

  const copyResult = () => {
    if (result) {
      navigator.clipboard.writeText(formatResult());
      // You could add a toast notification here
    }
  };

  return (
    <section id="calculator" className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Professional Age Calculator
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Choose your professional mode for specialized calculations and formats
            </p>
          </div>

          {/* Mode Selection */}
          <div className="mode-selector grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            {Object.entries(modes).map(([key, modeData]) => {
              const Icon = modeData.icon;
              const isActive = mode === key;

              // 定义固定的CSS类名
              const getActiveClasses = (color: string) => {
                switch (color) {
                  case 'blue': return 'border-blue-500 bg-blue-50';
                  case 'green': return 'border-green-500 bg-green-50';
                  case 'purple': return 'border-purple-500 bg-purple-50';
                  case 'pink': return 'border-pink-500 bg-pink-50';
                  case 'gray': return 'border-gray-500 bg-gray-50';
                  default: return 'border-blue-500 bg-blue-50';
                }
              };

              return (
                <div key={key} className="relative mode-details-container">
                  <button
                    onClick={() => setMode(key as CalculatorMode)}
                    className={`w-full p-4 rounded-lg border-2 transition-all text-left ${
                      isActive
                        ? getActiveClasses(modeData.color)
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Icon className={`w-5 h-5 mr-3 ${
                          isActive ? `text-${modeData.color}-600` : 'text-gray-500'
                        }`} />
                        <span className="font-semibold text-gray-900">{modeData.title}</span>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowModeDetails(showModeDetails === key ? null : key);
                        }}
                        className="p-1 rounded-full hover:bg-gray-100 transition-colors"
                        title="Show details"
                      >
                        <HelpCircle className="w-4 h-4 text-gray-400" />
                      </button>
                    </div>
                  </button>

                  {/* Details Tooltip */}
                  {showModeDetails === key && (
                    <div className="absolute top-full left-0 right-0 mt-2 p-4 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                      <p className="text-sm text-gray-600 mb-3">{modeData.description}</p>
                      <div className="space-y-1">
                        {modeData.features.map((feature, index) => (
                          <div key={index} className="text-xs text-gray-500 flex items-center">
                            <div className="w-1 h-1 bg-gray-400 rounded-full mr-2"></div>
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Calculator Interface */}
          <div className="bg-gray-50 rounded-xl mobile-section p-4 md:p-8">
            <div className="calculator-grid grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8">
              {/* Input Section */}
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {mode === 'slp' ? "Child's Birth Date" : "Birth Date"}
                  </label>
                  <input
                    type="date"
                    value={birthDate}
                    onChange={(e) => setBirthDate(e.target.value)}
                    placeholder="YYYY-MM-DD"
                    className="mobile-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                    lang="en"
                  />
                  {!birthDate && (
                    <p className="text-xs text-gray-500 mt-1">Please select the birth date</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {mode === 'professional' ? "Assessment Date" : 
                     mode === 'slp' ? "Evaluation Date" :
                     mode === 'psychology' ? "Testing Date" : "Calculate Age As Of"}
                  </label>
                  <input
                    type="date"
                    value={testDate}
                    onChange={(e) => setTestDate(e.target.value)}
                    placeholder="YYYY-MM-DD"
                    className="mobile-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    lang="en"
                  />
                </div>

                {mode === 'slp' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Assessment Type (Optional)
                    </label>
                    <select
                      value={assessmentTool}
                      onChange={(e) => setAssessmentTool(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select assessment...</option>
                      <option value="celf-5">CELF-5 (Language Assessment)</option>
                      <option value="ppvt-5">PPVT-5 (Vocabulary)</option>
                      <option value="gfta-3">GFTA-3 (Articulation)</option>
                      <option value="wisc-v">WISC-V (Cognitive)</option>
                      <option value="other">Other Assessment</option>
                    </select>
                  </div>
                )}

                <button
                  onClick={calculateAge}
                  className="mobile-button touch-target w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
                >
                  <Calculator className="w-5 h-5 mr-2" />
                  Calculate Age
                </button>
              </div>

              {/* Result Section */}
              <div className="space-y-6">
                {result ? (
                  <div className="bg-white rounded-lg p-6 border border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Chronological Age Result
                    </h3>
                    
                    <div className="space-y-4">
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <div className="text-sm text-blue-600 font-medium mb-1">
                          {mode === 'professional' ? 'Professional Format' :
                           mode === 'slp' ? 'SLP Report Format' :
                           mode === 'psychology' ? 'Psychology Report Format' : 'Standard Format'}
                        </div>
                        <div className="result-display text-xl md:text-2xl font-bold text-blue-900">
                          {formatResult()}
                        </div>
                      </div>

                      {mode === 'general' && (
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="text-center p-3 bg-gray-50 rounded">
                            <div className="font-semibold text-gray-900">{result.totalDays}</div>
                            <div className="text-gray-600">Total Days</div>
                          </div>
                          <div className="text-center p-3 bg-gray-50 rounded">
                            <div className="font-semibold text-gray-900">{result.totalWeeks}</div>
                            <div className="text-gray-600">Total Weeks</div>
                          </div>
                        </div>
                      )}

                      {/* Assessment Tool Specific Information */}
                      {mode === 'slp' && assessmentTool && (
                        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                          <div className="text-sm text-green-800">
                            <strong>Assessment Notes:</strong>
                            {assessmentTool === 'celf-5' && " Age range: 5;0 to 21;11. Consider language exposure for bilingual assessments."}
                            {assessmentTool === 'ppvt-5' && " Age range: 2;6 to 90+. Single-word receptive vocabulary assessment."}
                            {assessmentTool === 'gfta-3' && " Age range: 2;0 to 21;11. Articulation assessment."}
                            {assessmentTool === 'wisc-v' && " Age range: 6;0 to 16;11. Use exact chronological age for subtest selection."}
                          </div>
                        </div>
                      )}

                      <div className="flex gap-2">
                        <button
                          onClick={copyResult}
                          className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center"
                        >
                          <Copy className="w-4 h-4 mr-2" />
                          Copy Result
                        </button>
                        {mode !== 'general' && (
                          <button className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center">
                            <Download className="w-4 h-4 mr-2" />
                            Export
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-white rounded-lg p-6 border border-gray-200 text-center">
                    <Calculator className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">
                      Enter birth date and assessment date to calculate chronological age
                    </p>
                  </div>
                )}

                {/* Mode-specific tips */}
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">
                    {modes[mode].title} Tips:
                  </h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    {mode === 'professional' && (
                      <>
                        <p>• Use assessment date for standardized tests like WISC-V, CELF-5</p>
                        <p>• Y;M;D format meets IEP and evaluation report standards</p>
                      </>
                    )}
                    {mode === 'slp' && (
                      <>
                        <p>• Age calculated for norm-referenced assessment</p>
                        <p>• Consider developmental milestones for this age group</p>
                      </>
                    )}
                    {mode === 'psychology' && (
                      <>
                        <p>• Age calculated for psychoeducational evaluation</p>
                        <p>• Consider grade-level expectations and developmental norms</p>
                      </>
                    )}
                    {mode === 'general' && (
                      <>
                        <p>• Multiple format options available</p>
                        <p>• Perfect for personal use and general calculations</p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
