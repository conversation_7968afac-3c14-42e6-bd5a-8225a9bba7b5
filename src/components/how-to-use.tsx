import { Users, Brain, GraduationCap, Calculator, FileText, CheckCircle } from "lucide-react";

export function HowToUse() {
  const professionalGuides = [
    {
      icon: Users,
      title: "For Speech Therapists",
      description: "Calculate precise chronological age for speech and language assessments",
      steps: [
        "Select 'Speech Therapy' mode from the calculator",
        "Enter child's birth date and evaluation date",
        "Choose your assessment tool (CELF-5, PPVT-5, etc.)",
        "Get age in SLP report format for norm comparisons",
        "Copy result directly to your evaluation report"
      ],
      tip: "Use evaluation date for standardized assessments to ensure accurate norm-referenced scoring",
      color: "green"
    },
    {
      icon: Brain,
      title: "For School Psychologists",
      description: "Essential for psychoeducational evaluations and IEP assessments",
      steps: [
        "Select 'School Psychology' mode",
        "Input student's birth date and testing date",
        "Choose evaluation type (Initial, Re-evaluation, etc.)",
        "Review age-grade appropriateness analysis",
        "Export formatted result for evaluation reports"
      ],
      tip: "Consider grade-level expectations and developmental norms when interpreting results",
      color: "purple"
    },
    {
      icon: GraduationCap,
      title: "For Assessment Professionals",
      description: "Standardized testing and clinical evaluation support",
      steps: [
        "Use 'Professional Assessment' mode",
        "Enter precise birth and assessment dates",
        "Select appropriate output format (Y;M;D standard)",
        "Verify calculation meets testing requirements",
        "Integrate with your assessment protocol"
      ],
      tip: "Y;M;D format meets most standardized test administration requirements",
      color: "blue"
    }
  ];

  const generalSteps = [
    {
      number: "01",
      title: "Choose Your Mode",
      description: "Select the calculator mode that matches your professional needs or use general mode for basic calculations."
    },
    {
      number: "02", 
      title: "Enter Dates",
      description: "Input the birth date and assessment/calculation date. Use assessment date for professional evaluations."
    },
    {
      number: "03",
      title: "Get Results",
      description: "Receive precise chronological age in the appropriate format for your professional requirements."
    },
    {
      number: "04",
      title: "Use in Reports",
      description: "Copy or export results directly into your evaluation reports, IEPs, or assessment documentation."
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              How to Use for Professional Assessments
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Step-by-step guidance for different professional scenarios and assessment requirements
            </p>
          </div>

          {/* Quick Steps Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
            {generalSteps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-blue-600 font-bold text-lg">{step.number}</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h3>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </div>
            ))}
          </div>

          {/* Professional Guides */}
          <div className="space-y-12">
            {professionalGuides.map((guide, index) => {
              const Icon = guide.icon;
              return (
                <div key={index} className="bg-gray-50 rounded-2xl p-8">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
                    {/* Guide Header */}
                    <div>
                      <div className="flex items-center mb-4">
                        <div className={`w-12 h-12 bg-${guide.color}-100 rounded-lg flex items-center justify-center mr-4`}>
                          <Icon className={`w-6 h-6 text-${guide.color}-600`} />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-900">{guide.title}</h3>
                          <p className="text-gray-600">{guide.description}</p>
                        </div>
                      </div>

                      <div className={`bg-${guide.color}-50 border border-${guide.color}-200 rounded-lg p-4`}>
                        <div className="flex items-start">
                          <CheckCircle className={`w-5 h-5 text-${guide.color}-600 mr-2 mt-0.5 flex-shrink-0`} />
                          <div>
                            <p className={`text-${guide.color}-800 text-sm font-medium mb-1`}>Professional Tip:</p>
                            <p className={`text-${guide.color}-700 text-sm`}>{guide.tip}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Step-by-Step Instructions */}
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">Step-by-Step Process:</h4>
                      <div className="space-y-3">
                        {guide.steps.map((step, stepIndex) => (
                          <div key={stepIndex} className="flex items-start">
                            <div className={`w-6 h-6 bg-${guide.color}-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0`}>
                              <span className={`text-${guide.color}-600 font-semibold text-xs`}>{stepIndex + 1}</span>
                            </div>
                            <p className="text-gray-700 text-sm">{step}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Best Practices Section */}
          <div className="mt-16 bg-blue-50 rounded-2xl p-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Professional Best Practices
              </h3>
              <p className="text-gray-600">
                Essential guidelines for accurate and reliable age calculations in professional settings
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg p-6">
                <FileText className="w-8 h-8 text-blue-600 mb-3" />
                <h4 className="font-semibold text-gray-900 mb-2">Documentation Standards</h4>
                <p className="text-gray-600 text-sm">
                  Always document the exact dates used for age calculation in your reports. 
                  Include both birth date and assessment date for transparency.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6">
                <Calculator className="w-8 h-8 text-blue-600 mb-3" />
                <h4 className="font-semibold text-gray-900 mb-2">Verification Process</h4>
                <p className="text-gray-600 text-sm">
                  Double-check critical calculations, especially for eligibility determinations. 
                  Consider having a colleague verify important age calculations.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6">
                <CheckCircle className="w-8 h-8 text-blue-600 mb-3" />
                <h4 className="font-semibold text-gray-900 mb-2">Quality Assurance</h4>
                <p className="text-gray-600 text-sm">
                  Use consistent calculation methods across all assessments. 
                  Maintain records of calculation methods for audit purposes.
                </p>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="mt-16 text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Start Professional Calculations?
            </h3>
            <p className="text-gray-600 mb-6">
              Join thousands of professionals who trust our calculator for their assessment needs
            </p>
            <a 
              href="#calculator"
              className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Calculator className="w-5 h-5 mr-2" />
              Start Calculating Now
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
