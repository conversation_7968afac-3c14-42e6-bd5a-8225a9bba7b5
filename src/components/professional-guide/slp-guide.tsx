import { Users, <PERSON><PERSON>ir<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Cal<PERSON>tor, FileText } from "lucide-react";

export function SLPGuide() {
  const assessmentTools = [
    {
      name: "CELF-5",
      fullName: "Clinical Evaluation of Language Fundamentals - Fifth Edition",
      ageRange: "5;0 to 21;11",
      format: "Y;M format preferred",
      considerations: [
        "Use exact chronological age for norm selection",
        "Consider language exposure for bilingual clients",
        "Document assessment date clearly in reports",
        "Age affects subtest selection and scoring"
      ]
    },
    {
      name: "PPVT-5",
      fullName: "Peabody Picture Vocabulary Test - Fifth Edition",
      ageRange: "2;6 to 90+",
      format: "Y;M format standard",
      considerations: [
        "Single-word receptive vocabulary assessment",
        "Age determines starting point and ceiling",
        "Consider cultural and linguistic factors",
        "Useful for screening and progress monitoring"
      ]
    },
    {
      name: "GFTA-3",
      fullName: "Goldman-Fristoe Test of Articulation - Third Edition",
      ageRange: "2;0 to 21;11",
      format: "Y;M format required",
      considerations: [
        "Age-specific sound development expectations",
        "Dialectal variations consideration",
        "Developmental norms vary by age",
        "Progress monitoring requires consistent age calculation"
      ]
    }
  ];

  const reportingStandards = [
    {
      title: "Age Documentation",
      description: "Always document both birth date and assessment date",
      example: "Child's chronological age at time of assessment: 5 years, 3 months (5;3)"
    },
    {
      title: "Norm Reference",
      description: "Specify which age was used for norm comparison",
      example: "Standard scores based on chronological age norms for 5;3 age group"
    },
    {
      title: "Bilingual Considerations",
      description: "Document language exposure and cultural factors",
      example: "Chronological age: 6;2. English exposure: 3 years. Consider developmental expectations."
    }
  ];

  return (
    <section id="slp-guide" className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-semibold mb-6">
              <Users className="w-4 h-4 mr-2" />
              Speech-Language Pathology Guide
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Chronological Age Calculations for SLP Assessments
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Essential guidance for accurate age calculations in speech-language assessments, 
              ensuring valid norm-referenced scoring and appropriate intervention planning.
            </p>
          </div>

          {/* Assessment Tools Guide */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Common SLP Assessment Tools</h3>
            
            <div className="space-y-8">
              {assessmentTools.map((tool, index) => (
                <div key={index} className="bg-green-50 rounded-xl p-8 border border-green-100">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div>
                      <h4 className="text-xl font-bold text-gray-900 mb-2">{tool.name}</h4>
                      <p className="text-gray-700 font-medium mb-4">{tool.fullName}</p>
                      
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center">
                          <span className="font-medium text-gray-700 w-20">Age Range:</span>
                          <span className="text-green-700 font-semibold">{tool.ageRange}</span>
                        </div>
                        <div className="flex items-center">
                          <span className="font-medium text-gray-700 w-20">Format:</span>
                          <span className="text-green-700 font-semibold">{tool.format}</span>
                        </div>
                      </div>
                    </div>

                    <div className="lg:col-span-2">
                      <h5 className="font-semibold text-gray-900 mb-3">Key Considerations:</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {tool.considerations.map((consideration, idx) => (
                          <div key={idx} className="flex items-start">
                            <CheckCircle className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700 text-sm">{consideration}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* SLP-Specific Protocols */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">SLP Assessment Protocols</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                <div className="flex items-center mb-4">
                  <Calculator className="w-6 h-6 text-green-600 mr-3" />
                  <h4 className="text-lg font-bold text-gray-900">Pre-Assessment Calculation</h4>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h5 className="font-semibold text-gray-900 mb-2">Step 1: Verify Birth Date</h5>
                    <p className="text-gray-700 text-sm">
                      Confirm birth date from official records (birth certificate, medical records, 
                      or school enrollment documents). Never rely on parent/caregiver memory alone.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h5 className="font-semibold text-gray-900 mb-2">Step 2: Set Assessment Date</h5>
                    <p className="text-gray-700 text-sm">
                      Use the actual date of assessment administration, not the report writing date. 
                      For multi-session assessments, use the date of the specific subtest.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h5 className="font-semibold text-gray-900 mb-2">Step 3: Calculate and Verify</h5>
                    <p className="text-gray-700 text-sm">
                      Calculate chronological age and double-check the result. Consider having 
                      a colleague verify critical calculations for eligibility determinations.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                <div className="flex items-center mb-4">
                  <FileText className="w-6 h-6 text-green-600 mr-3" />
                  <h4 className="text-lg font-bold text-gray-900">Documentation Standards</h4>
                </div>
                
                <div className="space-y-4">
                  {reportingStandards.map((standard, index) => (
                    <div key={index} className="bg-green-50 rounded-lg p-4">
                      <h5 className="font-semibold text-green-900 mb-2">{standard.title}</h5>
                      <p className="text-green-800 text-sm mb-2">{standard.description}</p>
                      <div className="bg-white rounded p-2 border border-green-200">
                        <p className="text-green-700 text-xs font-mono">{standard.example}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Special Considerations */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Special Considerations for SLPs</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-amber-50 rounded-xl p-6 border border-amber-200">
                <div className="flex items-center mb-4">
                  <AlertTriangle className="w-6 h-6 text-amber-600 mr-3" />
                  <h4 className="font-bold text-amber-900">Bilingual Assessments</h4>
                </div>
                <div className="space-y-3 text-sm text-amber-800">
                  <p>• Use chronological age for norm comparison</p>
                  <p>• Document language exposure duration</p>
                  <p>• Consider cultural communication patterns</p>
                  <p>• Note any assessment modifications</p>
                </div>
              </div>

              <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
                <div className="flex items-center mb-4">
                  <BookOpen className="w-6 h-6 text-blue-600 mr-3" />
                  <h4 className="font-bold text-blue-900">Early Intervention</h4>
                </div>
                <div className="space-y-3 text-sm text-blue-800">
                  <p>• Consider corrected age for premature infants</p>
                  <p>• Document both chronological and corrected age</p>
                  <p>• Follow state-specific EI guidelines</p>
                  <p>• Transition planning at age milestones</p>
                </div>
              </div>

              <div className="bg-purple-50 rounded-xl p-6 border border-purple-200">
                <div className="flex items-center mb-4">
                  <Users className="w-6 h-6 text-purple-600 mr-3" />
                  <h4 className="font-bold text-purple-900">School-Age Services</h4>
                </div>
                <div className="space-y-3 text-sm text-purple-800">
                  <p>• Age affects service delivery models</p>
                  <p>• Consider grade-level expectations</p>
                  <p>• Transition planning requirements</p>
                  <p>• Progress monitoring protocols</p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Reference */}
          <div className="bg-green-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-6">SLP Quick Reference</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <h4 className="font-bold mb-3">CELF-5 Ages</h4>
                <p className="text-green-100 text-sm">5;0 to 21;11</p>
                <p className="text-green-100 text-sm">Use Y;M format</p>
              </div>
              
              <div>
                <h4 className="font-bold mb-3">PPVT-5 Ages</h4>
                <p className="text-green-100 text-sm">2;6 to 90+</p>
                <p className="text-green-100 text-sm">Standard Y;M format</p>
              </div>
              
              <div>
                <h4 className="font-bold mb-3">GFTA-3 Ages</h4>
                <p className="text-green-100 text-sm">2;0 to 21;11</p>
                <p className="text-green-100 text-sm">Y;M required</p>
              </div>
              
              <div>
                <h4 className="font-bold mb-3">Documentation</h4>
                <p className="text-green-100 text-sm">Birth + Assessment dates</p>
                <p className="text-green-100 text-sm">Age format: Y;M</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
