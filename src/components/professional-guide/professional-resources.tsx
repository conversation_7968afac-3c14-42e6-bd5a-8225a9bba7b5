import { BookOpen, ExternalLink, Download, Calculator, Users, Brain, GraduationCap } from "lucide-react";

export function ProfessionalResources() {
  const resources = [
    {
      category: "Professional Organizations",
      icon: Users,
      color: "blue",
      items: [
        {
          title: "ASHA - American Speech-Language-Hearing Association",
          description: "Professional standards and guidelines for speech-language pathology practice",
          url: "https://www.asha.org",
          type: "Website"
        },
        {
          title: "NASP - National Association of School Psychologists",
          description: "Best practices and ethical guidelines for school psychology",
          url: "https://www.nasponline.org",
          type: "Website"
        },
        {
          title: "CEC - Council for Exceptional Children",
          description: "Standards for special education assessment and intervention",
          url: "https://www.cec.sped.org",
          type: "Website"
        }
      ]
    },
    {
      category: "Assessment Guidelines",
      icon: BookOpen,
      color: "green",
      items: [
        {
          title: "WISC-V Administration and Scoring Manual",
          description: "Official guidelines for chronological age requirements and calculations",
          url: "#",
          type: "Manual"
        },
        {
          title: "CELF-5 Examiner's Manual",
          description: "Age calculation procedures for speech-language assessments",
          url: "#",
          type: "Manual"
        },
        {
          title: "IDEA Evaluation Guidelines",
          description: "Federal requirements for age-appropriate assessments",
          url: "#",
          type: "Guidelines"
        }
      ]
    },
    {
      category: "Calculation Tools",
      icon: Calculator,
      color: "purple",
      items: [
        {
          title: "Professional Age Calculator",
          description: "Our medical-grade chronological age calculator with multiple professional modes",
          url: "/#calculator",
          type: "Tool"
        },
        {
          title: "Age Calculation Verification Spreadsheet",
          description: "Excel template for double-checking age calculations",
          url: "#",
          type: "Download"
        },
        {
          title: "Assessment Date Tracker",
          description: "Template for tracking multiple assessment dates and ages",
          url: "#",
          type: "Download"
        }
      ]
    },
    {
      category: "Training Materials",
      icon: GraduationCap,
      color: "orange",
      items: [
        {
          title: "Age Calculation Best Practices Webinar",
          description: "Professional development training on accurate age calculations",
          url: "#",
          type: "Video"
        },
        {
          title: "Report Writing Standards Guide",
          description: "Templates and examples for professional age documentation",
          url: "#",
          type: "Guide"
        },
        {
          title: "Quality Assurance Checklist",
          description: "Systematic approach to ensuring calculation accuracy",
          url: "#",
          type: "Checklist"
        }
      ]
    }
  ];

  const quickLinks = [
    {
      title: "Professional Calculator",
      description: "Access our multi-mode professional calculator",
      icon: Calculator,
      url: "/#calculator",
      color: "blue"
    },
    {
      title: "Pearson Alternative Guide",
      description: "Complete migration guide from Pearson's discontinued calculator",
      icon: BookOpen,
      url: "/pearson-alternative",
      color: "green"
    },
    {
      title: "Technical Documentation",
      description: "Detailed information about our calculation methods",
      icon: Brain,
      url: "/calculation-methods-and-accuracy",
      color: "purple"
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-6">
              <BookOpen className="w-4 h-4 mr-2" />
              Professional Resources
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Additional Professional Resources
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Comprehensive collection of professional resources, guidelines, and tools to support 
              accurate chronological age calculations in assessment practice.
            </p>
          </div>

          {/* Resource Categories */}
          <div className="mb-16">
            <div className="space-y-12">
              {resources.map((category, index) => {
                const Icon = category.icon;
                return (
                  <div key={index} className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
                    <div className="flex items-center mb-6">
                      <div className={`w-12 h-12 bg-${category.color}-100 rounded-full flex items-center justify-center mr-4`}>
                        <Icon className={`w-6 h-6 text-${category.color}-600`} />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900">{category.category}</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {category.items.map((item, idx) => (
                        <div key={idx} className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors">
                          <div className="flex items-start justify-between mb-3">
                            <h4 className="font-semibold text-gray-900 text-sm leading-tight">{item.title}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium bg-${category.color}-100 text-${category.color}-800`}>
                              {item.type}
                            </span>
                          </div>
                          
                          <p className="text-gray-700 text-sm mb-4">{item.description}</p>
                          
                          <a 
                            href={item.url}
                            className={`inline-flex items-center text-${category.color}-600 hover:text-${category.color}-700 text-sm font-medium`}
                          >
                            {item.type === 'Download' ? (
                              <>
                                <Download className="w-4 h-4 mr-1" />
                                Download
                              </>
                            ) : (
                              <>
                                <ExternalLink className="w-4 h-4 mr-1" />
                                Access Resource
                              </>
                            )}
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Quick Access Links */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Quick Access</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {quickLinks.map((link, index) => {
                const Icon = link.icon;
                return (
                  <a 
                    key={index}
                    href={link.url}
                    className={`bg-${link.color}-50 rounded-xl p-6 border border-${link.color}-200 hover:bg-${link.color}-100 transition-colors block`}
                  >
                    <div className="text-center">
                      <div className={`w-16 h-16 bg-${link.color}-100 rounded-full flex items-center justify-center mx-auto mb-4`}>
                        <Icon className={`w-8 h-8 text-${link.color}-600`} />
                      </div>
                      <h4 className="font-bold text-gray-900 mb-2">{link.title}</h4>
                      <p className="text-gray-700 text-sm">{link.description}</p>
                    </div>
                  </a>
                );
              })}
            </div>
          </div>

          {/* Professional Support */}
          <div className="bg-blue-600 rounded-2xl p-8 text-white">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-4">Professional Support</h3>
              <p className="text-blue-100">
                Need additional support or have specific questions about chronological age calculations?
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h4 className="font-bold mb-2">Professional Consultation</h4>
                <p className="text-blue-100 text-sm mb-4">
                  Connect with our team of assessment professionals for guidance on complex cases
                </p>
                <a 
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors text-sm"
                >
                  Contact Support
                </a>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="w-8 h-8 text-white" />
                </div>
                <h4 className="font-bold mb-2">Training Workshops</h4>
                <p className="text-blue-100 text-sm mb-4">
                  Professional development workshops on assessment best practices and age calculations
                </p>
                <a 
                  href="#"
                  className="inline-flex items-center px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors text-sm"
                >
                  Learn More
                </a>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Calculator className="w-8 h-8 text-white" />
                </div>
                <h4 className="font-bold mb-2">Custom Solutions</h4>
                <p className="text-blue-100 text-sm mb-4">
                  Institutional licensing and custom integration solutions for organizations
                </p>
                <a 
                  href="#"
                  className="inline-flex items-center px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors text-sm"
                >
                  Get Quote
                </a>
              </div>
            </div>
          </div>

          {/* Footer CTA */}
          <div className="mt-16 text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Start Using Professional Standards?
            </h3>
            <p className="text-gray-600 mb-6">
              Apply these best practices with our professional-grade chronological age calculator
            </p>
            <a 
              href="/#calculator"
              className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Calculator className="w-5 h-5 mr-2" />
              Use Professional Calculator
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
