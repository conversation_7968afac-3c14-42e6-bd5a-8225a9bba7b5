import { FileText, CheckCircle, Copy, Alert<PERSON>ircle, BookOpen } from "lucide-react";

export function ReportWritingGuide() {
  const reportFormats = [
    {
      type: "SLP Evaluation Report",
      ageFormat: "Y;M (5;3)",
      example: "Child's chronological age at time of assessment: 5 years, 3 months (5;3)",
      context: "Used for norm-referenced scoring on CELF-5, PPVT-5, and other standardized assessments",
      requirements: [
        "Document both birth date and assessment date",
        "Use Y;M format for consistency with test manuals",
        "Include statement about norm group selection",
        "Note any factors affecting age interpretation"
      ]
    },
    {
      type: "Psychoeducational Evaluation",
      ageFormat: "Y;M;D (8;4;15)",
      example: "Student's chronological age at testing: 8 years, 4 months, 15 days (8;4;15)",
      context: "Required for WISC-V and other cognitive assessments with precise age requirements",
      requirements: [
        "Use Y;M;D format for cognitive assessments",
        "Document exact testing dates for each instrument",
        "Include age-grade comparison when relevant",
        "Address any age-related eligibility considerations"
      ]
    },
    {
      type: "IEP Documentation",
      ageFormat: "Y;M (7;2)",
      example: "Current chronological age: 7 years, 2 months. Age-appropriate expectations include...",
      context: "Used in Present Levels of Academic Achievement and Functional Performance (PLAAFP)",
      requirements: [
        "Connect age to developmental expectations",
        "Use age to justify goals and services",
        "Consider age in transition planning",
        "Document age-appropriate interventions"
      ]
    }
  ];

  const documentationExamples = [
    {
      section: "Assessment Administration",
      goodExample: "The CELF-5 was administered on March 15, 2024. The child's chronological age at testing was 6 years, 8 months (6;8), placing him in the 6;6-6;11 norm group.",
      poorExample: "The child is about 6 years old and was tested with the CELF-5.",
      explanation: "Good documentation includes exact dates, precise age, and norm group selection rationale."
    },
    {
      section: "Results Interpretation",
      goodExample: "Standard scores are based on chronological age norms for 8;4;15. Performance was compared to same-age peers using age-based normative data.",
      poorExample: "Test scores show the child is below average.",
      explanation: "Specify which age was used for scoring and how norms were selected."
    },
    {
      section: "Recommendations",
      goodExample: "Given the child's chronological age of 5;7, speech therapy services should focus on age-appropriate phonological processes and vocabulary development typical for this age range.",
      poorExample: "The child needs speech therapy.",
      explanation: "Connect recommendations to age-appropriate expectations and developmental norms."
    }
  ];

  return (
    <section id="report-writing" className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full text-sm font-semibold mb-6">
              <FileText className="w-4 h-4 mr-2" />
              Report Writing Guide
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Professional Report Writing Standards
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Learn how to properly document chronological age calculations in professional reports, 
              ensuring compliance with standards and clear communication of assessment results.
            </p>
          </div>

          {/* Report Format Standards */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Age Documentation by Report Type</h3>
            
            <div className="space-y-8">
              {reportFormats.map((format, index) => (
                <div key={index} className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div>
                      <h4 className="text-xl font-bold text-gray-900 mb-3">{format.type}</h4>
                      <div className="space-y-3">
                        <div>
                          <span className="text-sm font-medium text-gray-600">Format: </span>
                          <span className="font-mono text-orange-600 bg-orange-50 px-2 py-1 rounded">
                            {format.ageFormat}
                          </span>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-3">
                          <p className="text-gray-700 text-sm italic">"{format.example}"</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h5 className="font-semibold text-gray-900 mb-3">Context:</h5>
                      <p className="text-gray-700 text-sm mb-4">{format.context}</p>
                    </div>

                    <div>
                      <h5 className="font-semibold text-gray-900 mb-3">Requirements:</h5>
                      <div className="space-y-2">
                        {format.requirements.map((req, idx) => (
                          <div key={idx} className="flex items-start">
                            <CheckCircle className="w-4 h-4 text-orange-600 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700 text-sm">{req}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Documentation Examples */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Documentation Examples</h3>
            
            <div className="space-y-8">
              {documentationExamples.map((example, index) => (
                <div key={index} className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
                  <h4 className="text-lg font-bold text-gray-900 mb-6">{example.section}</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-green-50 rounded-lg p-6 border border-green-200">
                      <div className="flex items-center mb-3">
                        <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                        <h5 className="font-semibold text-green-900">Good Example</h5>
                      </div>
                      <p className="text-green-800 text-sm italic mb-3">"{example.goodExample}"</p>
                      <button className="flex items-center text-green-700 hover:text-green-800 text-sm">
                        <Copy className="w-4 h-4 mr-1" />
                        Copy Example
                      </button>
                    </div>

                    <div className="bg-red-50 rounded-lg p-6 border border-red-200">
                      <div className="flex items-center mb-3">
                        <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                        <h5 className="font-semibold text-red-900">Poor Example</h5>
                      </div>
                      <p className="text-red-800 text-sm italic mb-3">"{example.poorExample}"</p>
                      <p className="text-red-700 text-xs">❌ Lacks specificity and professional standards</p>
                    </div>
                  </div>

                  <div className="mt-4 bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <h6 className="font-semibold text-blue-900 mb-2">Why This Matters:</h6>
                    <p className="text-blue-800 text-sm">{example.explanation}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Report Templates */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Professional Report Templates</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center mb-4">
                  <BookOpen className="w-6 h-6 text-blue-600 mr-3" />
                  <h4 className="text-lg font-bold text-gray-900">SLP Assessment Report Template</h4>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4 font-mono text-sm">
                  <div className="space-y-2 text-gray-700">
                    <p><strong>Client Information:</strong></p>
                    <p>Name: [Client Name]</p>
                    <p>Date of Birth: [MM/DD/YYYY]</p>
                    <p>Assessment Date: [MM/DD/YYYY]</p>
                    <p>Chronological Age: [Y years, M months] ([Y;M])</p>
                    <p></p>
                    <p><strong>Assessment Results:</strong></p>
                    <p>The [Assessment Name] was administered on [Date]. </p>
                    <p>Standard scores are based on chronological age </p>
                    <p>norms for [Y;M] age group...</p>
                  </div>
                </div>
                
                <button className="mt-4 flex items-center text-blue-600 hover:text-blue-700 text-sm">
                  <Copy className="w-4 h-4 mr-1" />
                  Copy Template
                </button>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center mb-4">
                  <BookOpen className="w-6 h-6 text-purple-600 mr-3" />
                  <h4 className="text-lg font-bold text-gray-900">Psychoeducational Report Template</h4>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4 font-mono text-sm">
                  <div className="space-y-2 text-gray-700">
                    <p><strong>Student Information:</strong></p>
                    <p>Name: [Student Name]</p>
                    <p>Date of Birth: [MM/DD/YYYY]</p>
                    <p>Testing Dates: [MM/DD/YYYY - MM/DD/YYYY]</p>
                    <p>Chronological Age: [Y;M;D]</p>
                    <p>Grade: [Current Grade]</p>
                    <p></p>
                    <p><strong>Cognitive Assessment:</strong></p>
                    <p>The WISC-V was administered when [Student] </p>
                    <p>was [Y;M;D] years old. Scores are compared </p>
                    <p>to same-age peers...</p>
                  </div>
                </div>
                
                <button className="mt-4 flex items-center text-purple-600 hover:text-purple-700 text-sm">
                  <Copy className="w-4 h-4 mr-1" />
                  Copy Template
                </button>
              </div>
            </div>
          </div>

          {/* Common Mistakes */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Common Documentation Mistakes</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-red-50 rounded-xl p-6 border border-red-200">
                <div className="flex items-center mb-4">
                  <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
                  <h4 className="font-bold text-red-900">Vague Age References</h4>
                </div>
                <div className="space-y-3 text-sm text-red-800">
                  <p>❌ "The child is 5 years old"</p>
                  <p>❌ "Approximately 6 years of age"</p>
                  <p>✅ "Chronological age: 5;7"</p>
                  <p>✅ "Age at testing: 6;2;8"</p>
                </div>
              </div>

              <div className="bg-red-50 rounded-xl p-6 border border-red-200">
                <div className="flex items-center mb-4">
                  <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
                  <h4 className="font-bold text-red-900">Missing Date Documentation</h4>
                </div>
                <div className="space-y-3 text-sm text-red-800">
                  <p>❌ No birth date recorded</p>
                  <p>❌ No assessment date specified</p>
                  <p>✅ Both dates clearly documented</p>
                  <p>✅ Calculation method explained</p>
                </div>
              </div>

              <div className="bg-red-50 rounded-xl p-6 border border-red-200">
                <div className="flex items-center mb-4">
                  <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
                  <h4 className="font-bold text-red-900">Inconsistent Formats</h4>
                </div>
                <div className="space-y-3 text-sm text-red-800">
                  <p>❌ Mixing Y;M and Y;M;D formats</p>
                  <p>❌ Different formats within same report</p>
                  <p>✅ Consistent format throughout</p>
                  <p>✅ Format matches assessment requirements</p>
                </div>
              </div>
            </div>
          </div>

          {/* Best Practices Summary */}
          <div className="bg-orange-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-6 text-center">Report Writing Best Practices</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <h4 className="font-bold mb-2">Document Everything</h4>
                <p className="text-orange-100 text-sm">Birth date, assessment date, calculation method</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="w-6 h-6 text-white" />
                </div>
                <h4 className="font-bold mb-2">Use Standard Formats</h4>
                <p className="text-orange-100 text-sm">Y;M for SLP, Y;M;D for cognitive assessments</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <BookOpen className="w-6 h-6 text-white" />
                </div>
                <h4 className="font-bold mb-2">Follow Guidelines</h4>
                <p className="text-orange-100 text-sm">ASHA, NASP, and institutional standards</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <AlertCircle className="w-6 h-6 text-white" />
                </div>
                <h4 className="font-bold mb-2">Double-Check</h4>
                <p className="text-orange-100 text-sm">Verify calculations and review for accuracy</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
