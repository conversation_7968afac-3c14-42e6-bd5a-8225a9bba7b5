import { <PERSON>, Brain, GraduationCap, Book<PERSON><PERSON>, Calculator, ArrowRight } from "lucide-react";

export function ProfessionalGuideHero() {
  return (
    <section className="py-16 bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-6">
              <BookOpen className="w-4 h-4 mr-2" />
              Professional Assessment Guide
            </div>
            
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Complete Professional Guide to Chronological Age Calculations
            </h1>
            
            <p className="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Expert guidance for Speech Therapists, School Psychologists, and Assessment Professionals 
              on accurate chronological age calculations in professional evaluations and reports.
            </p>
          </div>

          {/* Professional Audiences */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Speech-Language Pathologists</h3>
              <p className="text-gray-600 mb-6">
                Essential guidance for CELF-5, PPVT-5, and other standardized speech-language assessments
              </p>
              <ul className="text-sm text-gray-600 space-y-2 text-left">
                <li>• Norm-referenced scoring requirements</li>
                <li>• Bilingual assessment considerations</li>
                <li>• Report writing standards</li>
                <li>• Early intervention protocols</li>
              </ul>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Brain className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">School Psychologists</h3>
              <p className="text-gray-600 mb-6">
                Comprehensive guidance for psychoeducational evaluations and IEP assessments
              </p>
              <ul className="text-sm text-gray-600 space-y-2 text-left">
                <li>• WISC-V administration requirements</li>
                <li>• Special education eligibility</li>
                <li>• IEP documentation standards</li>
                <li>• Transition planning protocols</li>
              </ul>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <GraduationCap className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Assessment Professionals</h3>
              <p className="text-gray-600 mb-6">
                Expert guidance for clinical evaluations and standardized testing protocols
              </p>
              <ul className="text-sm text-gray-600 space-y-2 text-left">
                <li>• Standardized test administration</li>
                <li>• Clinical evaluation protocols</li>
                <li>• Research data collection</li>
                <li>• Quality assurance standards</li>
              </ul>
            </div>
          </div>

          {/* Guide Overview */}
          <div className="bg-blue-600 rounded-2xl p-8 text-white">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <h2 className="text-2xl font-bold mb-4">What You'll Learn</h2>
                <p className="text-blue-100 mb-6">
                  This comprehensive guide covers everything you need to know about chronological age 
                  calculations in professional assessment contexts.
                </p>
                
                <div className="space-y-3">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-300 rounded-full mr-3"></div>
                    <span>Professional standards and compliance requirements</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-300 rounded-full mr-3"></div>
                    <span>Assessment-specific calculation protocols</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-300 rounded-full mr-3"></div>
                    <span>Report writing and documentation standards</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-300 rounded-full mr-3"></div>
                    <span>Common errors and how to avoid them</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-300 rounded-full mr-3"></div>
                    <span>Best practices for quality assurance</span>
                  </div>
                </div>
              </div>

              <div className="bg-white/10 rounded-xl p-6">
                <h3 className="text-xl font-bold mb-4">Quick Access</h3>
                <div className="space-y-3">
                  <a 
                    href="#slp-guide" 
                    className="flex items-center justify-between p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors"
                  >
                    <span>SLP Assessment Guide</span>
                    <ArrowRight className="w-4 h-4" />
                  </a>
                  <a 
                    href="#psychology-guide" 
                    className="flex items-center justify-between p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors"
                  >
                    <span>School Psychology Guide</span>
                    <ArrowRight className="w-4 h-4" />
                  </a>
                  <a 
                    href="#assessment-standards" 
                    className="flex items-center justify-between p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors"
                  >
                    <span>Assessment Standards</span>
                    <ArrowRight className="w-4 h-4" />
                  </a>
                  <a 
                    href="#report-writing" 
                    className="flex items-center justify-between p-3 bg-white/10 rounded-lg hover:bg-white/20 transition-colors"
                  >
                    <span>Report Writing Guide</span>
                    <ArrowRight className="w-4 h-4" />
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* CTA */}
          <div className="mt-16 text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Start Professional Calculations?
            </h3>
            <p className="text-gray-600 mb-6">
              Use our professional calculator while following this guide for best results
            </p>
            <a 
              href="/#calculator"
              className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Calculator className="w-5 h-5 mr-2" />
              Open Professional Calculator
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
