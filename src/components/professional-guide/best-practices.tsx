import { CheckCircle, Alert<PERSON>riangle, Shield, Users, Clock, Target } from "lucide-react";

export function BestPractices() {
  const practiceCategories = [
    {
      title: "Pre-Assessment Preparation",
      icon: Target,
      color: "blue",
      practices: [
        {
          practice: "Verify Birth Date Documentation",
          description: "Always confirm birth date from official records (birth certificate, medical records, school enrollment)",
          importance: "Critical",
          tip: "Never rely solely on parent/caregiver memory or unofficial documents"
        },
        {
          practice: "Plan Assessment Timeline",
          description: "Schedule assessments considering age-related factors and developmental appropriateness",
          importance: "High",
          tip: "Consider attention span, optimal testing times, and age-appropriate breaks"
        },
        {
          practice: "Review Age Requirements",
          description: "Check specific age ranges and requirements for each assessment tool before administration",
          importance: "Critical",
          tip: "Some assessments have strict age cutoffs that affect validity"
        }
      ]
    },
    {
      title: "During Assessment",
      icon: Clock,
      color: "green",
      practices: [
        {
          practice: "Document Exact Testing Dates",
          description: "Record the specific date for each assessment or subtest administered",
          importance: "Critical",
          tip: "For multi-session assessments, track dates for each component"
        },
        {
          practice: "Calculate Age Before Scoring",
          description: "Determine exact chronological age before beginning score calculations",
          importance: "High",
          tip: "Age determines which norms to use and affects score interpretation"
        },
        {
          practice: "Consider Developmental Context",
          description: "Interpret results within the context of age-appropriate expectations",
          importance: "High",
          tip: "Age affects not just scores but also behavioral observations and recommendations"
        }
      ]
    },
    {
      title: "Quality Assurance",
      icon: Shield,
      color: "purple",
      practices: [
        {
          practice: "Double-Check Calculations",
          description: "Verify age calculations independently, especially for critical decisions",
          importance: "Critical",
          tip: "Have a colleague verify calculations for eligibility determinations"
        },
        {
          practice: "Use Consistent Methods",
          description: "Apply the same calculation methodology across all assessments and reports",
          importance: "High",
          tip: "Document your calculation method for consistency and audit purposes"
        },
        {
          practice: "Maintain Calculation Records",
          description: "Keep detailed records of how ages were calculated and sources used",
          importance: "Medium",
          tip: "This supports professional accountability and quality assurance"
        }
      ]
    },
    {
      title: "Professional Communication",
      icon: Users,
      color: "orange",
      practices: [
        {
          practice: "Use Standard Terminology",
          description: "Employ consistent, professional language when discussing chronological age",
          importance: "Medium",
          tip: "Use 'chronological age' rather than 'actual age' or 'real age'"
        },
        {
          practice: "Explain Age Significance",
          description: "Help families and teams understand why precise age matters for assessments",
          importance: "High",
          tip: "Connect age to developmental expectations and assessment validity"
        },
        {
          practice: "Address Age-Related Concerns",
          description: "Proactively discuss age factors that might affect interpretation",
          importance: "Medium",
          tip: "Consider cultural, linguistic, and experiential factors alongside chronological age"
        }
      ]
    }
  ];

  const commonErrors = [
    {
      error: "Rounding or Estimating Ages",
      consequence: "Invalid norm selection and inaccurate score interpretation",
      prevention: "Always calculate exact age using precise dates",
      severity: "High"
    },
    {
      error: "Using Report Writing Date",
      consequence: "Age discrepancy affects score validity and legal compliance",
      prevention: "Use actual assessment administration date",
      severity: "High"
    },
    {
      error: "Inconsistent Age Formats",
      consequence: "Confusion in interpretation and potential scoring errors",
      prevention: "Establish and follow consistent format standards",
      severity: "Medium"
    },
    {
      error: "Ignoring Leap Year Effects",
      consequence: "Calculation errors that compound over time",
      prevention: "Use validated calculation tools that handle edge cases",
      severity: "Medium"
    },
    {
      error: "Poor Documentation",
      consequence: "Inability to verify calculations and potential audit issues",
      prevention: "Document birth date, assessment date, and calculation method",
      severity: "Medium"
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-semibold mb-6">
              <CheckCircle className="w-4 h-4 mr-2" />
              Professional Best Practices
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Best Practices for Chronological Age Calculations
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Evidence-based practices and professional standards to ensure accurate, reliable, 
              and ethically sound chronological age calculations in assessment contexts.
            </p>
          </div>

          {/* Practice Categories */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Professional Practice Framework</h3>
            
            <div className="space-y-12">
              {practiceCategories.map((category, index) => {
                const Icon = category.icon;
                return (
                  <div key={index} className={`bg-${category.color}-50 rounded-xl p-8 border border-${category.color}-200`}>
                    <div className="flex items-center mb-6">
                      <div className={`w-12 h-12 bg-${category.color}-100 rounded-full flex items-center justify-center mr-4`}>
                        <Icon className={`w-6 h-6 text-${category.color}-600`} />
                      </div>
                      <h4 className="text-xl font-bold text-gray-900">{category.title}</h4>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {category.practices.map((practice, idx) => (
                        <div key={idx} className="bg-white rounded-lg p-6 border border-gray-200">
                          <div className="flex items-start justify-between mb-3">
                            <h5 className="font-semibold text-gray-900 text-sm">{practice.practice}</h5>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              practice.importance === 'Critical' ? 'bg-red-100 text-red-800' :
                              practice.importance === 'High' ? 'bg-orange-100 text-orange-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {practice.importance}
                            </span>
                          </div>
                          
                          <p className="text-gray-700 text-sm mb-3">{practice.description}</p>
                          
                          <div className={`bg-${category.color}-50 rounded-lg p-3 border border-${category.color}-200`}>
                            <p className={`text-${category.color}-800 text-xs`}>
                              <strong>💡 Tip:</strong> {practice.tip}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Common Errors */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Common Errors & Prevention</h3>
            
            <div className="space-y-6">
              {commonErrors.map((error, index) => (
                <div key={index} className="bg-red-50 rounded-xl p-6 border border-red-200">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div>
                      <div className="flex items-center mb-2">
                        <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          error.severity === 'High' ? 'bg-red-100 text-red-800' : 'bg-orange-100 text-orange-800'
                        }`}>
                          {error.severity} Risk
                        </span>
                      </div>
                      <h4 className="font-bold text-red-900">{error.error}</h4>
                    </div>

                    <div>
                      <h5 className="font-semibold text-red-900 mb-2">Consequence:</h5>
                      <p className="text-red-800 text-sm">{error.consequence}</p>
                    </div>

                    <div>
                      <h5 className="font-semibold text-red-900 mb-2">Prevention:</h5>
                      <p className="text-red-800 text-sm">{error.prevention}</p>
                    </div>

                    <div className="flex items-center justify-center">
                      <div className="bg-white rounded-lg p-3 border border-red-300">
                        <CheckCircle className="w-6 h-6 text-green-600 mx-auto" />
                        <p className="text-green-700 text-xs text-center mt-1">Preventable</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quality Assurance Checklist */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Quality Assurance Checklist</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-green-50 rounded-xl p-6 border border-green-200">
                <h4 className="text-lg font-bold text-green-900 mb-4">Pre-Assessment Checklist</h4>
                
                <div className="space-y-3">
                  {[
                    "Birth date verified from official records",
                    "Assessment tools age ranges confirmed",
                    "Testing schedule planned appropriately",
                    "Calculation method determined",
                    "Documentation system prepared"
                  ].map((item, index) => (
                    <label key={index} className="flex items-center">
                      <input type="checkbox" className="mr-3 text-green-600" />
                      <span className="text-green-800 text-sm">{item}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
                <h4 className="text-lg font-bold text-blue-900 mb-4">Post-Assessment Checklist</h4>
                
                <div className="space-y-3">
                  {[
                    "Age calculated before scoring",
                    "Calculations double-checked",
                    "Appropriate norms selected",
                    "Age documented in report",
                    "Calculation method recorded"
                  ].map((item, index) => (
                    <label key={index} className="flex items-center">
                      <input type="checkbox" className="mr-3 text-blue-600" />
                      <span className="text-blue-800 text-sm">{item}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Professional Development */}
          <div className="bg-gray-900 rounded-2xl p-8 text-white">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-4">Continuous Professional Development</h3>
              <p className="text-gray-300">
                Stay current with best practices and maintain the highest standards of professional practice
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="font-bold mb-2">Regular Training</h4>
                <p className="text-gray-300 text-sm">
                  Participate in continuing education on assessment practices and age calculation standards
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h4 className="font-bold mb-2">Peer Consultation</h4>
                <p className="text-gray-300 text-sm">
                  Collaborate with colleagues to verify complex calculations and discuss challenging cases
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h4 className="font-bold mb-2">Quality Monitoring</h4>
                <p className="text-gray-300 text-sm">
                  Implement systematic quality assurance procedures and regular accuracy audits
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
