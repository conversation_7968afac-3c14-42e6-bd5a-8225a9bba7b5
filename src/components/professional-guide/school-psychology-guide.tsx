import { Brain, CheckCircle, AlertCircle, FileText, Users, GraduationCap } from "lucide-react";

export function SchoolPsychologyGuide() {
  const assessmentTools = [
    {
      name: "WISC-V",
      fullName: "Wechsler Intelligence Scale for Children - Fifth Edition",
      ageRange: "6;0 to 16;11",
      format: "Y;M;D format required",
      considerations: [
        "Exact age determines subtest selection",
        "Age affects scaled score calculations",
        "Critical for cognitive ability interpretation",
        "Required for special education eligibility"
      ]
    },
    {
      name: "WIAT-4",
      fullName: "Wechsler Individual Achievement Test - Fourth Edition",
      ageRange: "4;0 to 50;11",
      format: "Y;M format standard",
      considerations: [
        "Age-based vs grade-based norms available",
        "Choose appropriate norm group",
        "Document rationale for norm selection",
        "Consider retention/acceleration history"
      ]
    },
    {
      name: "BASC-3",
      fullName: "Behavior Assessment System for Children - Third Edition",
      ageRange: "2;0 to 21;11",
      format: "Y;M format preferred",
      considerations: [
        "Age determines form selection",
        "Developmental expectations vary by age",
        "Consider cultural and environmental factors",
        "Multiple informant perspectives needed"
      ]
    }
  ];

  const evaluationTypes = [
    {
      type: "Initial Evaluation",
      description: "First-time comprehensive assessment for special education eligibility",
      ageConsiderations: [
        "Precise age critical for eligibility determination",
        "Age affects developmental expectations",
        "Consider early intervention history",
        "Document age-appropriate interventions tried"
      ],
      timeline: "Must be completed within 60 days of consent"
    },
    {
      type: "Re-evaluation",
      description: "Periodic review of special education eligibility and needs",
      ageConsiderations: [
        "Compare current age to previous evaluation",
        "Consider developmental progress over time",
        "Age may affect continued eligibility",
        "Update age-appropriate goals and services"
      ],
      timeline: "Required at least every 3 years"
    },
    {
      type: "Transition Assessment",
      description: "Planning for post-secondary goals and adult services",
      ageConsiderations: [
        "Age 16+ requires transition planning",
        "Age of majority considerations (18+)",
        "Adult service eligibility requirements",
        "Vocational and independent living skills"
      ],
      timeline: "Begin by age 16, update annually"
    }
  ];

  return (
    <section id="psychology-guide" className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-purple-100 text-purple-800 rounded-full text-sm font-semibold mb-6">
              <Brain className="w-4 h-4 mr-2" />
              School Psychology Guide
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Chronological Age in Psychoeducational Evaluations
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Comprehensive guidance for school psychologists conducting evaluations, determining 
              eligibility, and developing appropriate educational interventions.
            </p>
          </div>

          {/* Assessment Tools */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Key Assessment Instruments</h3>
            
            <div className="space-y-6">
              {assessmentTools.map((tool, index) => (
                <div key={index} className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div>
                      <h4 className="text-xl font-bold text-gray-900 mb-2">{tool.name}</h4>
                      <p className="text-gray-600 text-sm mb-4">{tool.fullName}</p>
                      
                      <div className="space-y-2 text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Age Range: </span>
                          <span className="text-purple-700 font-semibold">{tool.ageRange}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Format: </span>
                          <span className="text-purple-700 font-semibold">{tool.format}</span>
                        </div>
                      </div>
                    </div>

                    <div className="lg:col-span-3">
                      <h5 className="font-semibold text-gray-900 mb-3">Assessment Considerations:</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {tool.considerations.map((consideration, idx) => (
                          <div key={idx} className="flex items-start">
                            <CheckCircle className="w-4 h-4 text-purple-600 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700 text-sm">{consideration}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Evaluation Types */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Evaluation Types & Age Considerations</h3>
            
            <div className="space-y-8">
              {evaluationTypes.map((evaluation, index) => (
                <div key={index} className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div>
                      <h4 className="text-xl font-bold text-gray-900 mb-3">{evaluation.type}</h4>
                      <p className="text-gray-600 mb-4">{evaluation.description}</p>
                      
                      <div className="bg-purple-50 rounded-lg p-3 border border-purple-200">
                        <p className="text-purple-800 text-sm font-medium">
                          Timeline: {evaluation.timeline}
                        </p>
                      </div>
                    </div>

                    <div className="lg:col-span-2">
                      <h5 className="font-semibold text-gray-900 mb-4">Age-Related Considerations:</h5>
                      <div className="space-y-3">
                        {evaluation.ageConsiderations.map((consideration, idx) => (
                          <div key={idx} className="flex items-start">
                            <div className="w-2 h-2 bg-purple-500 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                            <span className="text-gray-700">{consideration}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* IEP and Special Education */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">IEP Development & Special Education</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center mb-4">
                  <FileText className="w-6 h-6 text-purple-600 mr-3" />
                  <h4 className="text-lg font-bold text-gray-900">Age in IEP Documentation</h4>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h5 className="font-semibold text-gray-900 mb-2">Present Levels (PLAAFP)</h5>
                    <p className="text-gray-700 text-sm">
                      Document student's chronological age and how it relates to academic and 
                      functional performance. Compare to age-expected skills and milestones.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h5 className="font-semibold text-gray-900 mb-2">Goal Development</h5>
                    <p className="text-gray-700 text-sm">
                      Ensure IEP goals are age-appropriate and consider developmental expectations. 
                      Age affects the complexity and type of skills targeted.
                    </p>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h5 className="font-semibold text-gray-900 mb-2">Service Provision</h5>
                    <p className="text-gray-700 text-sm">
                      Age determines appropriate service delivery models, settings, and intensity. 
                      Consider age-appropriate peer interactions and environments.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center mb-4">
                  <GraduationCap className="w-6 h-6 text-purple-600 mr-3" />
                  <h4 className="text-lg font-bold text-gray-900">Eligibility Determination</h4>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                    <h5 className="font-semibold text-purple-900 mb-2">Developmental Delay</h5>
                    <p className="text-purple-800 text-sm">
                      Age is critical for determining developmental delay. Compare current 
                      functioning to age-expected milestones and developmental norms.
                    </p>
                  </div>
                  
                  <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                    <h5 className="font-semibold text-purple-900 mb-2">Intellectual Disability</h5>
                    <p className="text-purple-800 text-sm">
                      Chronological age affects adaptive behavior expectations and the 
                      determination of significant limitations in intellectual functioning.
                    </p>
                  </div>
                  
                  <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                    <h5 className="font-semibold text-purple-900 mb-2">Specific Learning Disability</h5>
                    <p className="text-purple-800 text-sm">
                      Age considerations include response to intervention, achievement 
                      discrepancies, and pattern of strengths and weaknesses analysis.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Legal and Ethical Considerations */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Legal & Ethical Considerations</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-red-50 rounded-xl p-6 border border-red-200">
                <div className="flex items-center mb-4">
                  <AlertCircle className="w-6 h-6 text-red-600 mr-3" />
                  <h4 className="font-bold text-red-900">IDEA Compliance</h4>
                </div>
                <div className="space-y-3 text-sm text-red-800">
                  <p>• Age affects service eligibility (birth-21)</p>
                  <p>• Transition planning required by age 16</p>
                  <p>• Age of majority considerations</p>
                  <p>• Evaluation timelines based on age</p>
                </div>
              </div>

              <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
                <div className="flex items-center mb-4">
                  <Users className="w-6 h-6 text-blue-600 mr-3" />
                  <h4 className="font-bold text-blue-900">Ethical Practice</h4>
                </div>
                <div className="space-y-3 text-sm text-blue-800">
                  <p>• Accurate age calculation is ethical imperative</p>
                  <p>• Document calculation methods</p>
                  <p>• Verify critical age determinations</p>
                  <p>• Consider cultural factors in interpretation</p>
                </div>
              </div>

              <div className="bg-green-50 rounded-xl p-6 border border-green-200">
                <div className="flex items-center mb-4">
                  <CheckCircle className="w-6 h-6 text-green-600 mr-3" />
                  <h4 className="font-bold text-green-900">Best Practices</h4>
                </div>
                <div className="space-y-3 text-sm text-green-800">
                  <p>• Double-check age calculations</p>
                  <p>• Use consistent calculation methods</p>
                  <p>• Document sources of birth date</p>
                  <p>• Consider developmental context</p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Reference */}
          <div className="bg-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-6">School Psychology Quick Reference</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <h4 className="font-bold mb-3">WISC-V</h4>
                <p className="text-purple-100 text-sm">Ages 6;0 to 16;11</p>
                <p className="text-purple-100 text-sm">Y;M;D format required</p>
              </div>
              
              <div>
                <h4 className="font-bold mb-3">Transition Planning</h4>
                <p className="text-purple-100 text-sm">Required by age 16</p>
                <p className="text-purple-100 text-sm">Update annually</p>
              </div>
              
              <div>
                <h4 className="font-bold mb-3">Re-evaluations</h4>
                <p className="text-purple-100 text-sm">Every 3 years max</p>
                <p className="text-purple-100 text-sm">Age affects eligibility</p>
              </div>
              
              <div>
                <h4 className="font-bold mb-3">Age of Majority</h4>
                <p className="text-purple-100 text-sm">Typically age 18</p>
                <p className="text-purple-100 text-sm">Rights transfer to student</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
