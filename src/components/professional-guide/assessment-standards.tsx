import { Shield, CheckCircle, Award, BookOpen, AlertTriangle } from "lucide-react";

export function AssessmentStandards() {
  const standards = [
    {
      organization: "ASHA",
      fullName: "American Speech-Language-Hearing Association",
      icon: Shield,
      color: "green",
      requirements: [
        "Use chronological age for all norm-referenced assessments",
        "Document exact birth date and assessment date",
        "Consider cultural and linguistic factors in interpretation",
        "Maintain accuracy in age calculations for ethical practice"
      ],
      guidelines: "ASHA Code of Ethics requires accurate and honest reporting of assessment results"
    },
    {
      organization: "NASP",
      fullName: "National Association of School Psychologists",
      icon: Award,
      color: "purple",
      requirements: [
        "Ensure accurate age calculations for all evaluations",
        "Use appropriate norms based on chronological age",
        "Document methodology for age determination",
        "Consider developmental context in interpretation"
      ],
      guidelines: "NASP Principles for Professional Ethics emphasize accurate assessment practices"
    },
    {
      organization: "IDEA",
      fullName: "Individuals with Disabilities Education Act",
      icon: BookOpen,
      color: "blue",
      requirements: [
        "Age-appropriate assessments and interventions",
        "Transition planning based on chronological age",
        "Eligibility determination considers age factors",
        "Services must be age-appropriate and evidence-based"
      ],
      guidelines: "Federal law requires age-appropriate evaluation and service provision"
    }
  ];

  const accuracyStandards = [
    {
      level: "Medical Grade",
      description: "99.99% accuracy required",
      applications: ["Clinical assessments", "Research studies", "Eligibility determinations"],
      tolerance: "Zero tolerance for calculation errors"
    },
    {
      level: "Professional Standard",
      description: "99.9% accuracy expected",
      applications: ["Educational assessments", "Progress monitoring", "Routine evaluations"],
      tolerance: "Minimal tolerance for minor discrepancies"
    },
    {
      level: "Administrative",
      description: "99% accuracy acceptable",
      applications: ["General documentation", "Screening purposes", "Preliminary assessments"],
      tolerance: "Some tolerance for rounding differences"
    }
  ];

  return (
    <section id="assessment-standards" className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-6">
              <Shield className="w-4 h-4 mr-2" />
              Professional Assessment Standards
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Industry Standards for Chronological Age Calculations
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Understanding the professional standards and requirements that govern accurate 
              chronological age calculations in assessment and evaluation contexts.
            </p>
          </div>

          {/* Professional Organizations */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Professional Organization Standards</h3>
            
            <div className="space-y-8">
              {standards.map((standard, index) => {
                const Icon = standard.icon;
                return (
                  <div key={index} className={`bg-${standard.color}-50 rounded-xl p-8 border border-${standard.color}-200`}>
                    <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                      <div className="text-center lg:text-left">
                        <div className={`w-16 h-16 bg-${standard.color}-100 rounded-full flex items-center justify-center mx-auto lg:mx-0 mb-4`}>
                          <Icon className={`w-8 h-8 text-${standard.color}-600`} />
                        </div>
                        <h4 className="text-xl font-bold text-gray-900 mb-2">{standard.organization}</h4>
                        <p className={`text-${standard.color}-700 text-sm font-medium`}>{standard.fullName}</p>
                      </div>

                      <div className="lg:col-span-2">
                        <h5 className="font-semibold text-gray-900 mb-4">Key Requirements:</h5>
                        <div className="space-y-3">
                          {standard.requirements.map((requirement, idx) => (
                            <div key={idx} className="flex items-start">
                              <CheckCircle className={`w-4 h-4 text-${standard.color}-600 mr-3 mt-0.5 flex-shrink-0`} />
                              <span className="text-gray-700 text-sm">{requirement}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h5 className="font-semibold text-gray-900 mb-3">Guidelines:</h5>
                        <div className={`bg-${standard.color}-100 rounded-lg p-4 border border-${standard.color}-200`}>
                          <p className={`text-${standard.color}-800 text-sm`}>{standard.guidelines}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Accuracy Standards */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Accuracy Standards by Application</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {accuracyStandards.map((standard, index) => (
                <div key={index} className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                  <div className="text-center mb-6">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Shield className="w-6 h-6 text-blue-600" />
                    </div>
                    <h4 className="text-lg font-bold text-gray-900 mb-2">{standard.level}</h4>
                    <p className="text-blue-600 font-semibold">{standard.description}</p>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h5 className="font-semibold text-gray-900 mb-2">Applications:</h5>
                      <ul className="space-y-1">
                        {standard.applications.map((app, idx) => (
                          <li key={idx} className="text-gray-600 text-sm flex items-center">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
                            {app}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-3">
                      <h5 className="font-semibold text-gray-900 mb-1 text-sm">Error Tolerance:</h5>
                      <p className="text-gray-700 text-xs">{standard.tolerance}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Compliance Checklist */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Professional Compliance Checklist</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-green-50 rounded-xl p-6 border border-green-200">
                <h4 className="text-lg font-bold text-green-900 mb-4">✅ Required Practices</h4>
                
                <div className="space-y-3">
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-green-900">Verify Birth Date</p>
                      <p className="text-green-700 text-sm">Use official documents (birth certificate, medical records)</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-green-900">Document Assessment Date</p>
                      <p className="text-green-700 text-sm">Record exact date of test administration</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-green-900">Use Appropriate Tools</p>
                      <p className="text-green-700 text-sm">Employ validated calculation methods</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-green-900">Double-Check Results</p>
                      <p className="text-green-700 text-sm">Verify calculations, especially for critical decisions</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-green-900">Document Methodology</p>
                      <p className="text-green-700 text-sm">Record calculation method and tools used</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-red-50 rounded-xl p-6 border border-red-200">
                <h4 className="text-lg font-bold text-red-900 mb-4">⚠️ Common Violations</h4>
                
                <div className="space-y-3">
                  <div className="flex items-start">
                    <AlertTriangle className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-red-900">Estimation or Rounding</p>
                      <p className="text-red-700 text-sm">Using approximate ages instead of precise calculations</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <AlertTriangle className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-red-900">Unverified Birth Dates</p>
                      <p className="text-red-700 text-sm">Relying on memory or unconfirmed information</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <AlertTriangle className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-red-900">Inconsistent Methods</p>
                      <p className="text-red-700 text-sm">Using different calculation approaches across assessments</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <AlertTriangle className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-red-900">Poor Documentation</p>
                      <p className="text-red-700 text-sm">Failing to record calculation details or sources</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <AlertTriangle className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-red-900">Ignoring Edge Cases</p>
                      <p className="text-red-700 text-sm">Not accounting for leap years or month variations</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quality Assurance */}
          <div className="bg-blue-600 rounded-2xl p-8 text-white">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-4">Quality Assurance Standards</h3>
              <p className="text-blue-100">
                Our calculator meets and exceeds all professional standards for accuracy and reliability
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-3xl font-bold mb-2">99.99%</div>
                <div className="text-blue-100 text-sm">Calculation Accuracy</div>
                <div className="text-blue-200 text-xs">Medical-grade precision</div>
              </div>
              
              <div>
                <div className="text-3xl font-bold mb-2">100%</div>
                <div className="text-blue-100 text-sm">Edge Case Coverage</div>
                <div className="text-blue-200 text-xs">Leap years, month variations</div>
              </div>
              
              <div>
                <div className="text-3xl font-bold mb-2">ASHA</div>
                <div className="text-blue-100 text-sm">Standards Compliant</div>
                <div className="text-blue-200 text-xs">Professional ethics aligned</div>
              </div>
              
              <div>
                <div className="text-3xl font-bold mb-2">NASP</div>
                <div className="text-blue-100 text-sm">Guidelines Met</div>
                <div className="text-blue-200 text-xs">Assessment best practices</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
