"use client";

import Link from "next/link";
import { useState } from "react";
import { Clock, Menu, X } from "lucide-react";

export function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link href="/" className="flex items-center gap-2">
          <div className="w-8 h-8 flex items-center justify-center bg-blue-600 rounded">
            <Clock className="w-5 h-5 text-white" />
          </div>
          <span className="font-bold text-xl">
            Chrono<span className="text-blue-600">Age</span>
          </span>
        </Link>

        <nav className="hidden md:flex space-x-8">
          <Link href="/" className="text-blue-600 font-medium">
            Calculator
          </Link>
          <Link href="/blog" className="text-gray-600 hover:text-blue-600 transition">
            Blog
          </Link>
          <Link href="/about" className="text-gray-600 hover:text-blue-600 transition">
            About
          </Link>
        </nav>

        <button
          className="md:hidden w-10 h-10 flex items-center justify-center text-gray-700"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-100">
          <nav className="flex flex-col p-4 space-y-4">
            <Link
              href="/"
              className="text-blue-600 font-medium py-2 border-b border-gray-100"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Calculator
            </Link>
            <Link
              href="/blog"
              className="text-gray-600 py-2 border-b border-gray-100"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Blog
            </Link>
            <Link
              href="/about"
              className="text-gray-600 py-2 border-b border-gray-100"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              About
            </Link>
          </nav>
        </div>
      )}
    </header>
  );
}
