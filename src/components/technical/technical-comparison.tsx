import { Check<PERSON>ircle, X, Zap, Shield } from "lucide-react";

export function TechnicalComparison() {
  const features = [
    {
      feature: "Calculation Accuracy",
      ourSolution: "99.99%",
      pearson: "99.99%",
      generic: "95-98%",
      description: "Precision of age calculations"
    },
    {
      feature: "Edge Case Handling",
      ourSolution: "100% Coverage",
      pearson: "95% Coverage", 
      generic: "Limited",
      description: "Leap years, month boundaries, etc."
    },
    {
      feature: "Performance (calculations/sec)",
      ourSolution: "1000+",
      pearson: "500+",
      generic: "100-500",
      description: "Throughput capacity"
    },
    {
      feature: "Validation Testing",
      ourSolution: "5000+ tests",
      pearson: "Unknown",
      generic: "Minimal",
      description: "Comprehensive test coverage"
    },
    {
      feature: "Professional Standards",
      ourSolution: "Full Compliance",
      pearson: "Full Compliance",
      generic: "Not Verified",
      description: "ASHA, NASP, medical standards"
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Technical Comparison
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              How our technical implementation compares to other solutions in the market.
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50 border-b border-gray-200">
                    <th className="text-left py-6 px-6 font-semibold text-gray-900">Technical Feature</th>
                    <th className="text-center py-6 px-4 font-semibold text-blue-600">Our Solution</th>
                    <th className="text-center py-6 px-4 font-semibold text-red-600">Pearson (Discontinued)</th>
                    <th className="text-center py-6 px-4 font-semibold text-gray-600">Generic Tools</th>
                  </tr>
                </thead>
                <tbody>
                  {features.map((feature, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-6">
                        <div>
                          <div className="font-medium text-gray-900">{feature.feature}</div>
                          <div className="text-sm text-gray-600">{feature.description}</div>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-center">
                        <div className="font-semibold text-blue-600">{feature.ourSolution}</div>
                      </td>
                      <td className="py-4 px-4 text-center">
                        <div className="font-semibold text-red-600">{feature.pearson}</div>
                      </td>
                      <td className="py-4 px-4 text-center">
                        <div className="font-semibold text-gray-600">{feature.generic}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-green-50 rounded-xl p-8 border border-green-200">
              <div className="flex items-center mb-6">
                <Shield className="w-8 h-8 text-green-600 mr-3" />
                <h3 className="text-xl font-bold text-green-900">Technical Advantages</h3>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-green-900">Enhanced Accuracy</div>
                    <div className="text-green-700 text-sm">Medical-grade precision with comprehensive validation</div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-green-900">Superior Performance</div>
                    <div className="text-green-700 text-sm">Optimized algorithms for high-volume usage</div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-green-900">Complete Edge Case Coverage</div>
                    <div className="text-green-700 text-sm">Handles all calendar complexities accurately</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 rounded-xl p-8 border border-blue-200">
              <div className="flex items-center mb-6">
                <Zap className="w-8 h-8 text-blue-600 mr-3" />
                <h3 className="text-xl font-bold text-blue-900">Innovation Highlights</h3>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-blue-900">Dual-Algorithm Validation</div>
                    <div className="text-blue-700 text-sm">Cross-validation ensures calculation accuracy</div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-blue-900">Real-time Performance Monitoring</div>
                    <div className="text-blue-700 text-sm">Continuous accuracy and speed tracking</div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="font-medium text-blue-900">Comprehensive Test Suite</div>
                    <div className="text-blue-700 text-sm">5000+ automated tests for reliability</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
