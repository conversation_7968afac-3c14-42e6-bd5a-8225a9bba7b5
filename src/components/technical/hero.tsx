import { Calculator, Shield, Zap, CheckCircle, Brain, Code } from "lucide-react";

export function TechnicalHero() {
  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 via-white to-blue-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-6">
              <Code className="w-4 h-4 mr-2" />
              Technical Documentation
            </div>
            
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Calculation Methods & Accuracy Standards
            </h1>
            
            <p className="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Comprehensive technical documentation of our medical-grade chronological age calculation 
              engine, designed for the highest standards of accuracy and reliability in professional assessments.
            </p>
          </div>

          {/* Key Technical Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-green-600" />
              </div>
              <div className="text-3xl font-bold text-green-600 mb-2">99.99%</div>
              <div className="text-gray-900 font-semibold mb-1">Accuracy Rate</div>
              <div className="text-gray-600 text-sm">Medical-grade precision</div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-blue-600" />
              </div>
              <div className="text-3xl font-bold text-blue-600 mb-2">&lt;1ms</div>
              <div className="text-gray-900 font-semibold mb-1">Response Time</div>
              <div className="text-gray-600 text-sm">Sub-millisecond calculations</div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-purple-600" />
              </div>
              <div className="text-3xl font-bold text-purple-600 mb-2">100%</div>
              <div className="text-gray-900 font-semibold mb-1">Edge Case Coverage</div>
              <div className="text-gray-600 text-sm">Leap years, month variations</div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Brain className="w-8 h-8 text-orange-600" />
              </div>
              <div className="text-3xl font-bold text-orange-600 mb-2">10,000+</div>
              <div className="text-gray-900 font-semibold mb-1">Test Cases</div>
              <div className="text-gray-600 text-sm">Comprehensive validation</div>
            </div>
          </div>

          {/* Technical Overview */}
          <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              Technical Architecture Overview
            </h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-bold text-gray-900 mb-4">Core Calculation Engine</h3>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-gray-900">Gregorian Calendar System</div>
                      <div className="text-gray-600 text-sm">ISO 8601 compliant date handling</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-gray-900">Leap Year Algorithm</div>
                      <div className="text-gray-600 text-sm">Proleptic Gregorian calendar rules</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-gray-900">Month Normalization</div>
                      <div className="text-gray-600 text-sm">Handles variable month lengths</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-gray-900">Timezone Normalization</div>
                      <div className="text-gray-600 text-sm">UTC-based calculations</div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-bold text-gray-900 mb-4">Quality Assurance</h3>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-gray-900">Automated Testing</div>
                      <div className="text-gray-600 text-sm">10,000+ test cases covering edge scenarios</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-gray-900">Cross-Validation</div>
                      <div className="text-gray-600 text-sm">Multiple algorithm verification</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-gray-900">Boundary Testing</div>
                      <div className="text-gray-600 text-sm">Century boundaries, leap year edges</div>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <div className="font-medium text-gray-900">Performance Monitoring</div>
                      <div className="text-gray-600 text-sm">Real-time accuracy tracking</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Standards Compliance */}
          <div className="bg-blue-600 rounded-2xl p-8 text-white">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-4">Standards Compliance</h2>
              <p className="text-blue-100">
                Our calculation methods meet and exceed international standards for chronological age determination
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold mb-2">ISO 8601</h3>
                <p className="text-blue-100 text-sm">International date and time standard compliance</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Calculator className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold mb-2">Medical Grade</h3>
                <p className="text-blue-100 text-sm">Meets medical device calculation standards</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold mb-2">Professional Standards</h3>
                <p className="text-blue-100 text-sm">ASHA, NASP, and institutional requirements</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
