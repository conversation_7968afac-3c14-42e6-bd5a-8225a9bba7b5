import { AlertTriangle, CheckCircle, Calendar, Code } from "lucide-react";

export function EdgeCaseHandling() {
  const edgeCases = [
    {
      category: "Leap Year Scenarios",
      icon: Calendar,
      color: "blue",
      cases: [
        {
          scenario: "Born on Feb 29, assessment on Feb 28",
          challenge: "Leap year birth date in non-leap year",
          solution: "Use Feb 28 as birthday equivalent in non-leap years",
          example: "Born: 2000-02-29, Assessment: 2001-02-28 → Age: 1;0;0"
        },
        {
          scenario: "Born on Feb 29, assessment on Mar 1",
          challenge: "Day-after-birthday in non-leap year",
          solution: "Calculate from Feb 28 equivalent",
          example: "Born: 2004-02-29, Assessment: 2005-03-01 → Age: 1;0;1"
        }
      ]
    },
    {
      category: "Month-End Variations",
      icon: AlertTriangle,
      color: "orange",
      cases: [
        {
          scenario: "Born on Jan 31, assessment in February",
          challenge: "Target month has fewer days",
          solution: "Use last day of target month",
          example: "Born: 2020-01-31, Assessment: 2020-02-15 → Age: 0;0;15"
        },
        {
          scenario: "Month borrowing with different lengths",
          challenge: "Complex month arithmetic",
          solution: "Normalize using actual month lengths",
          example: "Born: 2020-01-31, Assessment: 2020-04-01 → Age: 0;2;1"
        }
      ]
    },
    {
      category: "Century Boundaries",
      icon: Code,
      color: "purple",
      cases: [
        {
          scenario: "19th to 20th century transition",
          challenge: "Calendar system consistency",
          solution: "Proleptic Gregorian calendar rules",
          example: "Born: 1899-12-31, Assessment: 1900-01-01 → Age: 0;0;1"
        },
        {
          scenario: "Year 2000 leap year",
          challenge: "Century year leap year rules",
          solution: "Divisible by 400 rule application",
          example: "Born: 2000-02-29, Assessment: 2001-02-28 → Age: 1;0;0"
        }
      ]
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Edge Case Handling
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Comprehensive handling of calendar edge cases ensures accurate calculations 
              in all scenarios, including leap years, month variations, and century boundaries.
            </p>
          </div>

          <div className="space-y-12">
            {edgeCases.map((category, index) => {
              const Icon = category.icon;
              return (
                <div key={index} className={`bg-${category.color}-50 rounded-xl p-8 border border-${category.color}-200`}>
                  <div className="flex items-center mb-8">
                    <div className={`w-12 h-12 bg-${category.color}-100 rounded-full flex items-center justify-center mr-4`}>
                      <Icon className={`w-6 h-6 text-${category.color}-600`} />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900">{category.category}</h3>
                  </div>

                  <div className="space-y-8">
                    {category.cases.map((edgeCase, idx) => (
                      <div key={idx} className="bg-white rounded-lg p-6 border border-gray-200">
                        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                          <div>
                            <h4 className="font-bold text-gray-900 mb-2">Scenario</h4>
                            <p className="text-gray-700 text-sm">{edgeCase.scenario}</p>
                          </div>
                          
                          <div>
                            <h4 className="font-bold text-gray-900 mb-2">Challenge</h4>
                            <p className="text-gray-700 text-sm">{edgeCase.challenge}</p>
                          </div>
                          
                          <div>
                            <h4 className="font-bold text-gray-900 mb-2">Solution</h4>
                            <div className="flex items-start">
                              <CheckCircle className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                              <p className="text-gray-700 text-sm">{edgeCase.solution}</p>
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-bold text-gray-900 mb-2">Example</h4>
                            <div className="bg-gray-100 rounded p-3">
                              <code className="text-xs text-gray-800">{edgeCase.example}</code>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>

          <div className="mt-16 bg-green-50 rounded-2xl p-8 border border-green-200">
            <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              Edge Case Coverage Statistics
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-green-600 mb-2">100%</div>
                <div className="text-gray-900 font-semibold mb-1">Leap Year Cases</div>
                <div className="text-gray-600 text-sm">All scenarios covered</div>
              </div>
              
              <div>
                <div className="text-3xl font-bold text-green-600 mb-2">100%</div>
                <div className="text-gray-900 font-semibold mb-1">Month Variations</div>
                <div className="text-gray-600 text-sm">All month lengths handled</div>
              </div>
              
              <div>
                <div className="text-3xl font-bold text-green-600 mb-2">100%</div>
                <div className="text-gray-900 font-semibold mb-1">Century Boundaries</div>
                <div className="text-gray-600 text-sm">Historical accuracy maintained</div>
              </div>
              
              <div>
                <div className="text-3xl font-bold text-green-600 mb-2">0</div>
                <div className="text-gray-900 font-semibold mb-1">Known Edge Failures</div>
                <div className="text-gray-600 text-sm">Comprehensive testing</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
