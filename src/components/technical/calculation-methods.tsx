import { Code, Calendar, Zap, CheckCircle } from "lucide-react";

export function CalculationMethods() {
  const algorithms = [
    {
      name: "Primary Algorithm",
      description: "Core chronological age calculation using date arithmetic",
      complexity: "O(1)",
      accuracy: "99.99%",
      code: `function calculateChronologicalAge(birthDate: Date, assessmentDate: Date): AgeResult {
  // Normalize dates to UTC to avoid timezone issues
  const birth = new Date(birthDate.getFullYear(), birthDate.getMonth(), birthDate.getDate());
  const assessment = new Date(assessmentDate.getFullYear(), assessmentDate.getMonth(), assessmentDate.getDate());
  
  // Calculate years
  let years = assessment.getFullYear() - birth.getFullYear();
  
  // Calculate months
  let months = assessment.getMonth() - birth.getMonth();
  
  // Calculate days
  let days = assessment.getDate() - birth.getDate();
  
  // Handle negative days (assessment day < birth day)
  if (days < 0) {
    months--;
    const lastMonth = new Date(assessment.getFullYear(), assessment.getMonth(), 0);
    days += lastMonth.getDate();
  }
  
  // Handle negative months (assessment month < birth month)
  if (months < 0) {
    years--;
    months += 12;
  }
  
  return { years, months, days, isValid: true };
}`,
      features: [
        "Handles all calendar edge cases",
        "Timezone-independent calculations",
        "Leap year aware",
        "Month length normalization"
      ]
    },
    {
      name: "Validation Algorithm",
      description: "Cross-validation using alternative calculation method",
      complexity: "O(1)",
      accuracy: "99.99%",
      code: `function validateAgeCalculation(birthDate: Date, assessmentDate: Date): boolean {
  // Method 1: Date arithmetic (primary)
  const result1 = calculateChronologicalAge(birthDate, assessmentDate);
  
  // Method 2: Millisecond difference validation
  const birthTime = birthDate.getTime();
  const assessmentTime = assessmentDate.getTime();
  const diffMs = assessmentTime - birthTime;
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  // Convert back to years/months/days for comparison
  const totalDays = result1.years * 365.25 + result1.months * 30.44 + result1.days;
  
  // Allow small variance due to calendar irregularities
  const variance = Math.abs(totalDays - diffDays);
  
  return variance < 2; // Less than 2 days variance acceptable
}`,
      features: [
        "Dual-method verification",
        "Millisecond precision backup",
        "Variance tolerance checking",
        "Error detection and reporting"
      ]
    }
  ];

  const steps = [
    {
      step: 1,
      title: "Input Validation",
      description: "Validate and normalize input dates",
      details: [
        "Check date format validity",
        "Ensure assessment date ≥ birth date",
        "Normalize to UTC timezone",
        "Handle edge cases (Feb 29, etc.)"
      ]
    },
    {
      step: 2,
      title: "Core Calculation",
      description: "Perform primary age calculation",
      details: [
        "Calculate year difference",
        "Calculate month difference with borrowing",
        "Calculate day difference with month normalization",
        "Handle negative values appropriately"
      ]
    },
    {
      step: 3,
      title: "Edge Case Handling",
      description: "Apply special rules for calendar edge cases",
      details: [
        "Leap year birth date handling",
        "Month-end date adjustments",
        "Century boundary calculations",
        "DST transition considerations"
      ]
    },
    {
      step: 4,
      title: "Validation & Output",
      description: "Validate results and format output",
      details: [
        "Cross-validate with alternative method",
        "Check result reasonableness",
        "Format according to requested standard",
        "Return structured result object"
      ]
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Calculation Methods & Algorithms
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Detailed technical documentation of our chronological age calculation algorithms, 
              designed for maximum accuracy and reliability in professional assessment contexts.
            </p>
          </div>

          {/* Calculation Steps */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Calculation Process</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {steps.map((step, index) => (
                <div key={index} className="bg-gray-50 rounded-xl p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <span className="text-xl font-bold text-blue-600">{step.step}</span>
                  </div>
                  
                  <h4 className="text-lg font-bold text-gray-900 mb-3">{step.title}</h4>
                  <p className="text-gray-600 text-sm mb-4">{step.description}</p>
                  
                  <div className="space-y-2">
                    {step.details.map((detail, idx) => (
                      <div key={idx} className="flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-xs">{detail}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Algorithm Details */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Algorithm Implementation</h3>
            
            <div className="space-y-12">
              {algorithms.map((algorithm, index) => (
                <div key={index} className="bg-gray-50 rounded-xl p-8">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div>
                      <div className="flex items-center mb-4">
                        <Code className="w-6 h-6 text-blue-600 mr-3" />
                        <h4 className="text-xl font-bold text-gray-900">{algorithm.name}</h4>
                      </div>
                      
                      <p className="text-gray-700 mb-6">{algorithm.description}</p>
                      
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Complexity:</span>
                          <span className="font-mono text-blue-600">{algorithm.complexity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Accuracy:</span>
                          <span className="font-semibold text-green-600">{algorithm.accuracy}</span>
                        </div>
                      </div>
                      
                      <div className="mt-6">
                        <h5 className="font-semibold text-gray-900 mb-3">Key Features:</h5>
                        <div className="space-y-2">
                          {algorithm.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start">
                              <CheckCircle className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-700 text-sm">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="lg:col-span-2">
                      <div className="bg-gray-900 rounded-lg p-6 overflow-x-auto">
                        <pre className="text-green-400 text-sm">
                          <code>{algorithm.code}</code>
                        </pre>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="bg-blue-50 rounded-2xl p-8 border border-blue-200">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              Performance Characteristics
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-8 h-8 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-blue-600 mb-2">&lt;1ms</div>
                <div className="text-gray-900 font-semibold mb-1">Execution Time</div>
                <div className="text-gray-600 text-sm">Average calculation time</div>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Calendar className="w-8 h-8 text-green-600" />
                </div>
                <div className="text-2xl font-bold text-green-600 mb-2">1000+</div>
                <div className="text-gray-900 font-semibold mb-1">Calculations/sec</div>
                <div className="text-gray-600 text-sm">Throughput capacity</div>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-purple-600" />
                </div>
                <div className="text-2xl font-bold text-purple-600 mb-2">100%</div>
                <div className="text-gray-900 font-semibold mb-1">Reliability</div>
                <div className="text-gray-600 text-sm">Uptime and consistency</div>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Code className="w-8 h-8 text-orange-600" />
                </div>
                <div className="text-2xl font-bold text-orange-600 mb-2">O(1)</div>
                <div className="text-gray-900 font-semibold mb-1">Complexity</div>
                <div className="text-gray-600 text-sm">Constant time algorithm</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
