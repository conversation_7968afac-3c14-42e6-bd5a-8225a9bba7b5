import { Shield, Target, CheckCircle, AlertTriangle } from "lucide-react";

export function AccuracyStandards() {
  const standards = [
    {
      level: "Medical Grade",
      accuracy: "99.99%",
      tolerance: "±0 days",
      applications: ["Clinical assessments", "Research studies", "Legal documentation"],
      requirements: [
        "Zero tolerance for calculation errors",
        "Validated against medical standards",
        "Audit trail for all calculations",
        "Cross-validation required"
      ]
    },
    {
      level: "Professional",
      accuracy: "99.9%",
      tolerance: "±1 day",
      applications: ["Educational assessments", "Psychological evaluations", "IEP documentation"],
      requirements: [
        "Minimal tolerance for rounding",
        "Professional organization compliance",
        "Quality assurance protocols",
        "Regular accuracy monitoring"
      ]
    },
    {
      level: "Standard",
      accuracy: "99%",
      tolerance: "±2 days",
      applications: ["General documentation", "Administrative purposes", "Screening tools"],
      requirements: [
        "Acceptable for routine use",
        "Basic validation protocols",
        "Standard error handling",
        "Regular system checks"
      ]
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Accuracy Standards & Validation
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Our calculation engine meets the highest standards of accuracy across different 
              professional applications and use cases.
            </p>
          </div>

          <div className="space-y-8">
            {standards.map((standard, index) => (
              <div key={index} className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                  <div className="text-center lg:text-left">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto lg:mx-0 mb-4">
                      <Shield className="w-8 h-8 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{standard.level}</h3>
                    <div className="text-2xl font-bold text-blue-600 mb-1">{standard.accuracy}</div>
                    <div className="text-gray-600 text-sm">Accuracy Rate</div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Tolerance</h4>
                    <div className="text-lg font-bold text-green-600 mb-2">{standard.tolerance}</div>
                    <p className="text-gray-600 text-sm">Maximum acceptable variance</p>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Applications</h4>
                    <div className="space-y-1">
                      {standard.applications.map((app, idx) => (
                        <div key={idx} className="text-gray-700 text-sm">• {app}</div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Requirements</h4>
                    <div className="space-y-2">
                      {standard.requirements.map((req, idx) => (
                        <div key={idx} className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700 text-sm">{req}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-16 bg-blue-600 rounded-2xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-4">Our Accuracy Achievement</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <div className="text-3xl font-bold mb-2">99.99%</div>
                <div className="text-blue-100">Verified Accuracy</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">10,000+</div>
                <div className="text-blue-100">Test Cases</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">Zero</div>
                <div className="text-blue-100">Known Errors</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
