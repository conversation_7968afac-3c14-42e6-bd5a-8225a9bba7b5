import { Shield, Calculator, Clock, CheckCircle } from "lucide-react";

export function WhyChooseUs() {
  return (
    <section className="py-8 md:py-12 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-8">
          <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
            Why Choose Our Age Calculator?
          </h2>
          <p className="text-base text-gray-600">
            Professional-grade accuracy with user-friendly design
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Calculator className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Precise Calculations
            </h3>
            <p className="text-gray-600 text-sm">
              Uses advanced date arithmetic to handle leap years, varying month lengths, and time zones accurately.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Privacy Protected
            </h3>
            <p className="text-gray-600 text-sm">
              All calculations are performed locally in your browser. No personal data is stored or transmitted.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Instant Results
            </h3>
            <p className="text-gray-600 text-sm">
              Get immediate, real-time calculations without waiting for server processing or page reloads.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Professional Grade
            </h3>
            <p className="text-gray-600 text-sm">
              Meets the accuracy standards required for medical assessments and educational evaluations.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
