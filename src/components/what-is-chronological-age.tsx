import { Calendar, Users, Brain, GraduationCap } from "lucide-react";

export function WhatIsChronologicalAge() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              What Is Chronological Age?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Understanding chronological age is fundamental to accurate assessment and evaluation.
            </p>
          </div>

          <div className="mb-16">
            <div className="bg-blue-50 rounded-2xl p-8 border border-blue-200">
              <div className="flex items-start">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-6 flex-shrink-0">
                  <Calendar className="w-8 h-8 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-blue-900 mb-4">
                    Professional Definition
                  </h3>
                  <p className="text-blue-800 text-lg leading-relaxed mb-4">
                    Chronological age is the amount of time that has elapsed from birth to a given date, 
                    typically expressed in years, months, and days.
                  </p>
                  <p className="text-blue-700">
                    In professional assessment contexts, chronological age serves as the foundation for norm-referenced 
                    scoring and developmental expectations.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-green-50 rounded-xl p-6 border border-green-200 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-green-600" />
              </div>
              <h4 className="text-lg font-bold text-green-900 mb-3">Norm-Referenced Scoring</h4>
              <p className="text-green-800 text-sm">
                Standardized assessments compare performance to age-matched peers.
              </p>
            </div>

            <div className="bg-purple-50 rounded-xl p-6 border border-purple-200 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Brain className="w-8 h-8 text-purple-600" />
              </div>
              <h4 className="text-lg font-bold text-purple-900 mb-3">Developmental Expectations</h4>
              <p className="text-purple-800 text-sm">
                Age determines appropriate developmental milestones.
              </p>
            </div>

            <div className="bg-orange-50 rounded-xl p-6 border border-orange-200 text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <GraduationCap className="w-8 h-8 text-orange-600" />
              </div>
              <h4 className="text-lg font-bold text-orange-900 mb-3">Educational Planning</h4>
              <p className="text-orange-800 text-sm">
                Age-appropriate curriculum and service planning.
              </p>
            </div>

            <div className="bg-blue-50 rounded-xl p-6 border border-blue-200 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="text-lg font-bold text-blue-900 mb-3">Legal Compliance</h4>
              <p className="text-blue-800 text-sm">
                Age-based eligibility and legal protections.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
