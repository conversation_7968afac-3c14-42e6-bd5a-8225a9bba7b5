import { Shield, Calculator, Clock, CheckCircle } from "lucide-react";

export function ProfessionalHero() {
  return (
    <section className="hero-section relative py-8 md:py-12 lg:py-16 overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-grid-pattern"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-5xl mx-auto text-center">
          {/* Professional Badge */}
          <div className="mb-6">
            <span className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">
              <Shield className="w-4 h-4 mr-2" />
              Professional Medical & Educational Tool
            </span>
          </div>

          {/* Main Headline */}
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 md:mb-6 leading-tight">
            Professional Chronological Age Calculator
          </h1>

          {/* Subtitle */}
          <p className="text-lg md:text-xl text-gray-700 mb-4 max-w-3xl mx-auto leading-relaxed">
            Trusted Alternative to Pearson's Discontinued Calculator
          </p>

          {/* Value Proposition */}
          <p className="text-base md:text-lg text-gray-600 mb-8 max-w-4xl mx-auto">
            Medical-grade accuracy for Speech Therapists, School Psychologists, and Assessment Professionals. 
            Calculate precise chronological age in professional formats with the reliability you need for 
            evaluations, IEP reports, and standardized assessments.
          </p>

          {/* Trust Indicators */}
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <div className="flex items-center text-gray-700 bg-white/70 px-4 py-2 rounded-lg shadow-sm">
              <Calculator className="w-5 h-5 text-blue-600 mr-2" />
              <span className="font-medium">Medical-Grade Accuracy</span>
            </div>
            <div className="flex items-center text-gray-700 bg-white/70 px-4 py-2 rounded-lg shadow-sm">
              <Shield className="w-5 h-5 text-blue-600 mr-2" />
              <span className="font-medium">HIPAA Compliant</span>
            </div>
            <div className="flex items-center text-gray-700 bg-white/70 px-4 py-2 rounded-lg shadow-sm">
              <Clock className="w-5 h-5 text-blue-600 mr-2" />
              <span className="font-medium">Instant Results</span>
            </div>
            <div className="flex items-center text-gray-700 bg-white/70 px-4 py-2 rounded-lg shadow-sm">
              <CheckCircle className="w-5 h-5 text-blue-600 mr-2" />
              <span className="font-medium">Pearson-Level Reliability</span>
            </div>
          </div>

          {/* Professional User Types */}
          <div className="mb-8">
            <p className="text-sm text-gray-600 mb-4">Trusted by professionals in:</p>
            <div className="flex flex-wrap justify-center gap-3 text-sm">
              <span className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full font-medium">
                Speech-Language Pathology
              </span>
              <span className="bg-green-50 text-green-700 px-3 py-1 rounded-full font-medium">
                School Psychology
              </span>
              <span className="bg-purple-50 text-purple-700 px-3 py-1 rounded-full font-medium">
                Educational Assessment
              </span>
              <span className="bg-orange-50 text-orange-700 px-3 py-1 rounded-full font-medium">
                Clinical Evaluation
              </span>
            </div>
          </div>

          {/* CTA */}
          <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center items-center">
            <a
              href="#calculator"
              className="mobile-button touch-target inline-flex items-center px-6 md:px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg w-full sm:w-auto justify-center"
            >
              Start Professional Calculation
              <Calculator className="w-5 h-5 ml-2" />
            </a>
            <a
              href="#features"
              className="mobile-button touch-target inline-flex items-center px-6 py-3 text-blue-600 font-semibold hover:text-blue-700 transition-colors w-full sm:w-auto justify-center"
            >
              View Professional Features
            </a>
          </div>

          {/* Pearson Replacement Notice */}
          <div className="mt-8 p-4 bg-amber-50 border border-amber-200 rounded-lg max-w-2xl mx-auto">
            <p className="text-amber-800 text-sm">
              <strong>Important Notice:</strong> Since Pearson discontinued their age calculator, 
              thousands of professionals have switched to our reliable alternative. 
              <a href="/pearson-alternative" className="underline hover:no-underline">
                Learn about the transition →
              </a>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
