"use client";

import { useState, useEffect } from "react";
import { differenceInYears, differenceInMonths, differenceInDays, subYears, subMonths } from "date-fns";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Calendar, Copy, Check, Calculator, Stethoscope, Baby } from "lucide-react";
import { trackEvent } from "@/components/google-analytics";

type CalculatorMode = 'standard' | 'professional' | 'preemie';

export function AgeCalculator() {
  const [mode, setMode] = useState<CalculatorMode>('standard');
  const [birthDate, setBirthDate] = useState("");
  const [calculationDate, setCalculationDate] = useState("");
  const [gestationalWeeks, setGestationalWeeks] = useState("");
  const [gestationalDays, setGestationalDays] = useState("");
  const [result, setResult] = useState<{
    years: number;
    months: number;
    days: number;
    correctedAge?: { years: number; months: number; days: number };
    adjustmentWeeks?: number;
  } | null>(null);
  const [copied, setCopied] = useState(false);
  const [error, setError] = useState("");

  // 在客户端设置当前日期，避免hydration错误
  useEffect(() => {
    setCalculationDate(new Date().toISOString().split('T')[0]);
  }, []);

  const calculateAge = () => {
    setError("");

    if (!birthDate) {
      setError("Please enter your date of birth");
      return;
    }

    // Validate gestational age for preemie mode
    if (mode === 'preemie') {
      if (!gestationalWeeks) {
        setError("Please enter gestational age for premature baby calculation");
        return;
      }
      const gestWeeks = parseInt(gestationalWeeks);
      if (gestWeeks < 20 || gestWeeks > 42) {
        setError("Gestational age should be between 20-42 weeks");
        return;
      }
    }

    const birth = new Date(birthDate);
    const calculation = new Date(calculationDate);

    if (birth > calculation) {
      setError("Date of birth cannot be in the future");
      return;
    }

    // Calculate chronological age
    const years = differenceInYears(calculation, birth);
    const afterYears = subYears(calculation, years);
    const months = differenceInMonths(afterYears, birth);
    const afterMonths = subMonths(afterYears, months);
    const days = differenceInDays(afterMonths, birth);

    const resultData: {
      years: number;
      months: number;
      days: number;
      correctedAge?: { years: number; months: number; days: number };
      adjustmentWeeks?: number;
    } = { years, months, days };

    // Calculate corrected age for preemie mode
    if (mode === 'preemie' && gestationalWeeks) {
      const gestWeeks = parseInt(gestationalWeeks);
      const gestDays = parseInt(gestationalDays) || 0;

      const totalGestationalDays = gestWeeks * 7 + gestDays;
      const fullTermDays = 40 * 7; // 40 weeks = 280 days
      const adjustmentDays = fullTermDays - totalGestationalDays;
      const adjustmentWeeks = Math.round(adjustmentDays / 7 * 10) / 10;

      const correctedDate = new Date(calculation.getTime() - (adjustmentDays * 24 * 60 * 60 * 1000));

      if (correctedDate >= birth) {
        const corrYears = differenceInYears(correctedDate, birth);
        const corrAfterYears = subYears(correctedDate, corrYears);
        const corrMonths = differenceInMonths(corrAfterYears, birth);
        const corrAfterMonths = subMonths(corrAfterYears, corrMonths);
        const corrDays = differenceInDays(corrAfterMonths, birth);

        resultData.correctedAge = { years: corrYears, months: corrMonths, days: corrDays };
      } else {
        resultData.correctedAge = { years: 0, months: 0, days: 0 };
      }

      resultData.adjustmentWeeks = adjustmentWeeks;
    }

    setResult(resultData);

    // Track calculation event
    trackEvent('calculate_age', 'calculator', mode, years);
  };

  const copyResult = async () => {
    if (!result) return;
    
    const resultText = `${result.years} Years, ${result.months} Months, ${result.days} Days`;
    
    try {
      await navigator.clipboard.writeText(resultText);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);

      // Track copy event
      trackEvent('copy_result', 'calculator', mode);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-3xl mb-8 shadow-2xl">
              <Calculator className="w-10 h-10 text-white" />
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Professional Age Calculator
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Calculate precise chronological age with professional-grade accuracy. Trusted by healthcare professionals and educational institutions worldwide.
            </p>
          </div>

          <Card className="bg-white/80 backdrop-blur-sm shadow-2xl border-0 rounded-3xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-8">
              {/* Mode Selection */}
              <div className="flex flex-wrap justify-center gap-4 mb-8">
                <button
                  onClick={() => {
                    setMode('standard');
                    trackEvent('mode_change', 'calculator', 'standard');
                  }}
                  className={`group flex items-center gap-3 px-6 py-4 rounded-2xl text-sm font-semibold transition-all duration-300 ${
                    mode === 'standard'
                      ? 'bg-white text-blue-600 shadow-lg scale-105'
                      : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105'
                  }`}
                >
                  <div className={`w-8 h-8 rounded-xl flex items-center justify-center ${
                    mode === 'standard' ? 'bg-blue-100' : 'bg-white/20'
                  }`}>
                    <Calculator className="w-4 h-4" />
                  </div>
                  <div className="text-left">
                    <div className="font-bold">Standard</div>
                    <div className="text-xs opacity-80">Basic calculation</div>
                  </div>
                </button>
                <button
                  onClick={() => {
                    setMode('professional');
                    trackEvent('mode_change', 'calculator', 'professional');
                  }}
                  className={`group flex items-center gap-3 px-6 py-4 rounded-2xl text-sm font-semibold transition-all duration-300 ${
                    mode === 'professional'
                      ? 'bg-white text-blue-600 shadow-lg scale-105'
                      : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105'
                  }`}
                >
                  <div className={`w-8 h-8 rounded-xl flex items-center justify-center ${
                    mode === 'professional' ? 'bg-blue-100' : 'bg-white/20'
                  }`}>
                    <Stethoscope className="w-4 h-4" />
                  </div>
                  <div className="text-left">
                    <div className="font-bold">Professional</div>
                    <div className="text-xs opacity-80">Medical & educational</div>
                  </div>
                </button>
                <button
                  onClick={() => {
                    setMode('preemie');
                    trackEvent('mode_change', 'calculator', 'preemie');
                  }}
                  className={`group flex items-center gap-3 px-6 py-4 rounded-2xl text-sm font-semibold transition-all duration-300 ${
                    mode === 'preemie'
                      ? 'bg-white text-blue-600 shadow-lg scale-105'
                      : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105'
                  }`}
                >
                  <div className={`w-8 h-8 rounded-xl flex items-center justify-center ${
                    mode === 'preemie' ? 'bg-blue-100' : 'bg-white/20'
                  }`}>
                    <Baby className="w-4 h-4" />
                  </div>
                  <div className="text-left">
                    <div className="font-bold">Preemie</div>
                    <div className="text-xs opacity-80">Corrected age</div>
                  </div>
                </button>
              </div>

              {/* Mode Description */}
              <div className="text-center">
                <div className="bg-white/20 rounded-2xl p-6 backdrop-blur-sm">
                  <div className="text-lg font-semibold mb-2">
                    {mode === 'standard' && "Standard Chronological Age Calculator"}
                    {mode === 'professional' && "Professional Assessment Calculator"}
                    {mode === 'preemie' && "Premature Baby Age Calculator"}
                  </div>
                  <p className="text-white/90">
                    {mode === 'standard' && "Calculate exact chronological age in years, months, and days with precision"}
                    {mode === 'professional' && "Designed for medical assessments, educational evaluations, and professional documentation"}
                    {mode === 'preemie' && "Calculate both chronological and corrected age for premature babies"}
                  </p>
                </div>
              </div>
            </CardHeader>
          <CardContent className="p-8 md:p-12">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className="group">
                <label htmlFor="birthDate" className="block text-lg font-semibold text-gray-800 mb-4">
                  Date of Birth
                </label>
                <div className="relative">
                  <input
                    type="date"
                    id="birthDate"
                    value={birthDate}
                    onChange={(e) => setBirthDate(e.target.value)}
                    placeholder="Select birth date"
                    className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl text-gray-700 text-lg font-medium focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 bg-gray-50 hover:bg-white group-hover:border-blue-300"
                    required
                  />
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                      <Calendar className="w-5 h-5 text-blue-600" />
                    </div>
                  </div>
                </div>
                <p className="text-sm text-gray-500 mt-2">Enter your birth date for accurate calculation</p>
              </div>

              <div className="group">
                <label htmlFor="calculationDate" className="block text-lg font-semibold text-gray-800 mb-4">
                  Age as of Date
                </label>
                <div className="relative">
                  <input
                    type="date"
                    id="calculationDate"
                    value={calculationDate}
                    onChange={(e) => setCalculationDate(e.target.value)}
                    placeholder="mm/dd/yyyy"
                    className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl text-gray-700 text-lg font-medium focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 bg-gray-50 hover:bg-white group-hover:border-blue-300"
                  />
                  <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                      <Calendar className="w-5 h-5 text-green-600" />
                    </div>
                  </div>
                </div>
                <p className="text-sm text-gray-500 mt-2">Calculate age as of this specific date</p>
              </div>
            </div>

            {/* Gestational Age Fields for Preemie Mode */}
            {mode === 'preemie' && (
              <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-3xl p-8 mb-8 border border-pink-200">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-pink-500 rounded-2xl flex items-center justify-center mr-4">
                    <Baby className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">Gestational Age Information</h3>
                    <p className="text-gray-600">Required for corrected age calculation</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="group">
                    <label htmlFor="gestationalWeeks" className="block text-lg font-semibold text-gray-800 mb-4">
                      Gestational Age (Weeks) *
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        id="gestationalWeeks"
                        value={gestationalWeeks}
                        onChange={(e) => setGestationalWeeks(e.target.value)}
                        min="20"
                        max="42"
                        className="w-full px-6 py-4 border-2 border-pink-200 rounded-2xl text-gray-700 text-lg font-medium focus:outline-none focus:ring-4 focus:ring-pink-500/20 focus:border-pink-500 transition-all duration-300 bg-white hover:border-pink-300"
                        placeholder="e.g., 32"
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                        <div className="w-10 h-10 bg-pink-100 rounded-xl flex items-center justify-center">
                          <span className="text-pink-600 font-bold text-sm">W</span>
                        </div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500 mt-2">Gestational age at birth (20-42 weeks)</p>
                  </div>

                  <div className="group">
                    <label htmlFor="gestationalDays" className="block text-lg font-semibold text-gray-800 mb-4">
                      Additional Days (Optional)
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        id="gestationalDays"
                        value={gestationalDays}
                        onChange={(e) => setGestationalDays(e.target.value)}
                        min="0"
                        max="6"
                        className="w-full px-6 py-4 border-2 border-pink-200 rounded-2xl text-gray-700 text-lg font-medium focus:outline-none focus:ring-4 focus:ring-pink-500/20 focus:border-pink-500 transition-all duration-300 bg-white hover:border-pink-300"
                        placeholder="0-6"
                      />
                      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                        <div className="w-10 h-10 bg-pink-100 rounded-xl flex items-center justify-center">
                          <span className="text-pink-600 font-bold text-sm">D</span>
                        </div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500 mt-2">Additional days beyond weeks (0-6)</p>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-pink-100 rounded-2xl border border-pink-200">
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-pink-500 rounded-lg flex items-center justify-center mr-3">
                      <span className="text-white text-xs font-bold">!</span>
                    </div>
                    <p className="text-pink-800 text-sm font-medium">
                      Corrected age adjusts for prematurity by subtracting the weeks born early from chronological age
                    </p>
                  </div>
                </div>
              </div>
            )}

            {error && (
              <div className="mb-8 p-6 bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200 rounded-2xl">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-red-500 rounded-xl flex items-center justify-center mr-4">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-bold text-red-800 mb-1">Input Error</h4>
                    <p className="text-red-700 text-lg">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-center">
              <Button
                onClick={calculateAge}
                className="group px-12 py-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-bold text-xl rounded-2xl shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
              >
                <div className="flex items-center gap-4">
                  <div className="w-8 h-8 bg-white/20 rounded-xl flex items-center justify-center group-hover:rotate-12 transition-transform duration-300">
                    <Calculator className="w-5 h-5" />
                  </div>
                  Calculate Age
                </div>
              </Button>
            </div>

            {result && (
              <div className="mt-12 pt-8 border-t-2 border-gray-200">
                {mode === 'preemie' && result.correctedAge ? (
                  <div className="space-y-8">
                    <div className="text-center mb-8">
                      <h3 className="text-3xl font-bold text-gray-900 mb-2">Age Calculation Results</h3>
                      <p className="text-gray-600">Both chronological and corrected age for premature baby</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl p-8 border-2 border-blue-200 shadow-lg">
                        <div className="flex items-center mb-6">
                          <div className="w-12 h-12 bg-blue-500 rounded-2xl flex items-center justify-center mr-4">
                            <Calendar className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-blue-800">Chronological Age</h3>
                            <p className="text-blue-600 text-sm">Actual time since birth</p>
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-4xl font-bold text-blue-600 mb-4">
                            {result.years}y {result.months}m {result.days}d
                          </div>
                          <div className="grid grid-cols-3 gap-3">
                            <div className="bg-white rounded-xl p-3 shadow-sm">
                              <div className="text-2xl font-bold text-blue-600">{result.years}</div>
                              <div className="text-xs text-blue-500 font-medium">Years</div>
                            </div>
                            <div className="bg-white rounded-xl p-3 shadow-sm">
                              <div className="text-2xl font-bold text-blue-600">{result.months}</div>
                              <div className="text-xs text-blue-500 font-medium">Months</div>
                            </div>
                            <div className="bg-white rounded-xl p-3 shadow-sm">
                              <div className="text-2xl font-bold text-blue-600">{result.days}</div>
                              <div className="text-xs text-blue-500 font-medium">Days</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-pink-50 to-purple-50 rounded-3xl p-8 border-2 border-pink-200 shadow-lg">
                        <div className="flex items-center mb-6">
                          <div className="w-12 h-12 bg-pink-500 rounded-2xl flex items-center justify-center mr-4">
                            <Baby className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-pink-800">Corrected Age</h3>
                            <p className="text-pink-600 text-sm">Age adjusted for prematurity</p>
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-4xl font-bold text-pink-600 mb-4">
                            {result.correctedAge.years}y {result.correctedAge.months}m {result.correctedAge.days}d
                          </div>
                          <div className="grid grid-cols-3 gap-3">
                            <div className="bg-white rounded-xl p-3 shadow-sm">
                              <div className="text-2xl font-bold text-pink-600">{result.correctedAge.years}</div>
                              <div className="text-xs text-pink-500 font-medium">Years</div>
                            </div>
                            <div className="bg-white rounded-xl p-3 shadow-sm">
                              <div className="text-2xl font-bold text-pink-600">{result.correctedAge.months}</div>
                              <div className="text-xs text-pink-500 font-medium">Months</div>
                            </div>
                            <div className="bg-white rounded-xl p-3 shadow-sm">
                              <div className="text-2xl font-bold text-pink-600">{result.correctedAge.days}</div>
                              <div className="text-xs text-pink-500 font-medium">Days</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-8">
                    <div className="text-center mb-8">
                      <h3 className="text-3xl font-bold text-gray-900 mb-2">
                        {mode === 'professional' ? 'Assessment Age Result' : 'Your Age Is'}
                      </h3>
                      <p className="text-gray-600">
                        {mode === 'professional' ? 'Professional-grade calculation for medical and educational use' : 'Precise chronological age calculation'}
                      </p>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl p-8 border-2 border-blue-200 shadow-lg">
                      <div className="text-center mb-8">
                        <div className="text-5xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 mb-4">
                          {result.years}y {result.months}m {result.days}d
                        </div>
                        <div className="text-xl text-gray-600 font-medium">
                          {result.years} Years, {result.months} Months, {result.days} Days
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-6">
                        <div className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                          <div className="w-12 h-12 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <span className="text-blue-600 font-bold text-lg">Y</span>
                          </div>
                          <div className="text-3xl font-bold text-gray-800 mb-2">{result.years}</div>
                          <div className="text-sm text-gray-600 font-medium">Years</div>
                        </div>
                        <div className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                          <div className="w-12 h-12 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <span className="text-green-600 font-bold text-lg">M</span>
                          </div>
                          <div className="text-3xl font-bold text-gray-800 mb-2">{result.months}</div>
                          <div className="text-sm text-gray-600 font-medium">Months</div>
                        </div>
                        <div className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                          <div className="w-12 h-12 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <span className="text-purple-600 font-bold text-lg">D</span>
                          </div>
                          <div className="text-3xl font-bold text-gray-800 mb-2">{result.days}</div>
                          <div className="text-sm text-gray-600 font-medium">Days</div>
                        </div>
                      </div>
                    </div>

                    {mode === 'professional' && (
                      <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl p-6 border border-gray-200 shadow-lg">
                          <div className="flex items-center mb-4">
                            <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                              </svg>
                            </div>
                            <h4 className="font-bold text-gray-900 text-lg">Total Time Lived</h4>
                          </div>
                          <div className="space-y-3">
                            <div className="flex justify-between items-center p-3 bg-white rounded-xl">
                              <span className="text-gray-600 font-medium">Total Days:</span>
                              <span className="font-bold text-gray-900 text-lg">{Math.floor((new Date(calculationDate).getTime() - new Date(birthDate).getTime()) / (1000 * 60 * 60 * 24)).toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-white rounded-xl">
                              <span className="text-gray-600 font-medium">Total Months:</span>
                              <span className="font-bold text-gray-900 text-lg">{(result.years * 12 + result.months).toFixed(1)}</span>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-white rounded-xl">
                              <span className="text-gray-600 font-medium">Total Weeks:</span>
                              <span className="font-bold text-gray-900 text-lg">{Math.floor((new Date(calculationDate).getTime() - new Date(birthDate).getTime()) / (1000 * 60 * 60 * 24 * 7)).toLocaleString()}</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-gradient-to-br from-gray-50 to-green-50 rounded-2xl p-6 border border-gray-200 shadow-lg">
                          <div className="flex items-center mb-4">
                            <div className="w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center mr-3">
                              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            </div>
                            <h4 className="font-bold text-gray-900 text-lg">Assessment Details</h4>
                          </div>
                          <div className="space-y-3">
                            <div className="p-3 bg-white rounded-xl">
                              <div className="text-gray-600 font-medium mb-1">Birth Date:</div>
                              <div className="font-bold text-gray-900">{new Date(birthDate).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</div>
                            </div>
                            <div className="p-3 bg-white rounded-xl">
                              <div className="text-gray-600 font-medium mb-1">Assessment Date:</div>
                              <div className="font-bold text-gray-900">{new Date(calculationDate).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</div>
                            </div>
                            <div className="p-3 bg-white rounded-xl">
                              <div className="text-gray-600 font-medium mb-1">Precision Level:</div>
                              <div className="font-bold text-green-700">Day-level accuracy</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {mode === 'preemie' && result.adjustmentWeeks && (
                  <div className="mt-8 p-6 bg-gradient-to-r from-amber-50 to-orange-50 rounded-2xl border border-amber-200">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-amber-500 rounded-xl flex items-center justify-center mr-4">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-bold text-amber-900 mb-1">Gestational Age Adjustment</h4>
                        <p className="text-amber-800 text-lg">
                          <strong>{Math.abs(result.adjustmentWeeks)} weeks</strong>
                          {result.adjustmentWeeks > 0 ? ' subtracted' : ' added'} for gestational age correction
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="mt-8 text-center">
                  <Button
                    onClick={copyResult}
                    variant="outline"
                    className="group inline-flex items-center px-8 py-4 text-lg font-semibold text-blue-600 bg-blue-50 hover:bg-blue-100 border-2 border-blue-200 hover:border-blue-300 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  >
                    {copied ? (
                      <>
                        <div className="w-8 h-8 bg-green-100 rounded-xl flex items-center justify-center mr-3">
                          <Check className="w-5 h-5 text-green-600" />
                        </div>
                        <span className="text-green-600 font-bold">Copied to Clipboard!</span>
                      </>
                    ) : (
                      <>
                        <div className="w-8 h-8 bg-blue-100 rounded-xl flex items-center justify-center mr-3 group-hover:rotate-12 transition-transform duration-300">
                          <Copy className="w-5 h-5 text-blue-600" />
                        </div>
                        Copy Result to Clipboard
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        </div>
      </div>
    </section>
  );
}
