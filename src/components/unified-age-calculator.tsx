"use client";

import { useState, useEffect } from "react";
import { Calculator, Copy, Download, <PERSON>, <PERSON>, User, Baby, HelpCircle, Calendar, Check } from "lucide-react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { calculateChronologicalAge, formatAgeForProfessional, calculateCorrectedAge, type AgeCalculationResult } from "@/lib/age-calculator";
import { trackEvent } from "@/components/google-analytics";

type CalculatorMode = 'standard' | 'preemie';

export function UnifiedAgeCalculator() {
  const [mode, setMode] = useState<CalculatorMode>('standard');
  const [birthDate, setBirthDate] = useState('');
  const [testDate, setTestDate] = useState("");
  const [gestationalWeeks, setGestationalWeeks] = useState("");
  const [gestationalDays, setGestationalDays] = useState("");
  const [result, setResult] = useState<AgeCalculationResult | null>(null);
  const [correctedResult, setCorrectedResult] = useState<AgeCalculationResult | null>(null);
  const [showModeDetails, setShowModeDetails] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [isClient, setIsClient] = useState(false);

  const modes = {
    standard: {
      title: "Standard Calculator",
      icon: Calculator,
      description: "For all professional and personal use",
      color: "blue",
      features: ["Y;M;D format", "Multiple output formats", "Professional documentation"]
    },
    preemie: {
      title: "Preemie Calculator",
      icon: Baby,
      description: "For premature babies with corrected age",
      color: "pink",
      features: ["Corrected age calculation", "Gestational age input", "Both chronological & corrected ages"]
    }
  };

  // 在客户端设置当前日期，避免hydration错误
  useEffect(() => {
    setIsClient(true);
    setTestDate(new Date().toISOString().split('T')[0]);
  }, []);

  const getActiveClasses = (color: string) => {
    const colorMap = {
      blue: 'border-blue-500 bg-blue-50',
      green: 'border-green-500 bg-green-50',
      purple: 'border-purple-500 bg-purple-50',
      pink: 'border-pink-500 bg-pink-50',
      gray: 'border-gray-500 bg-gray-50'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getGradientClasses = (color: string) => {
    const gradientMap = {
      blue: 'from-blue-500 to-blue-600',
      green: 'from-green-500 to-green-600',
      purple: 'from-purple-500 to-purple-600',
      pink: 'from-pink-500 to-pink-600',
      gray: 'from-gray-500 to-gray-600'
    };
    return gradientMap[color as keyof typeof gradientMap] || gradientMap.blue;
  };

  const calculateAge = () => {
    if (!birthDate || !testDate) return;

    trackEvent('calculate_age', 'calculator', mode);

    const birth = new Date(birthDate);
    const test = new Date(testDate);

    if (birth > test) {
      alert("Birth date cannot be after the assessment date");
      return;
    }

    const ageResult = calculateChronologicalAge(birth, test);
    setResult(ageResult);

    // Calculate corrected age for preemie mode
    if (mode === 'preemie' && gestationalWeeks) {
      const gestationalAge = parseInt(gestationalWeeks) + (gestationalDays ? parseInt(gestationalDays) / 7 : 0);
      const correctedAgeResult = calculateCorrectedAge(birth, gestationalAge, test);
      setCorrectedResult(correctedAgeResult);
    } else {
      setCorrectedResult(null);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
    trackEvent('copy_result', 'calculator', mode);
  };

  const formatResultForCopy = () => {
    if (!result) return '';
    
    const formatted = formatAgeForProfessional(result);
    const birthDateFormatted = new Date(birthDate).toLocaleDateString('en-US');
    const testDateFormatted = new Date(testDate).toLocaleDateString('en-US');
    
    let output = `Chronological Age Calculation\n`;
    output += `Birth Date: ${birthDateFormatted}\n`;
    output += `Assessment Date: ${testDateFormatted}\n`;
    output += `Chronological Age: ${formatted}\n`;
    
    if (correctedResult && mode === 'preemie') {
      const correctedFormatted = formatAgeForProfessional(correctedResult);
      output += `Corrected Age: ${correctedFormatted}\n`;
      output += `Gestational Age: ${gestationalWeeks} weeks ${gestationalDays || 0} days\n`;
    }
    
    return output;
  };

  return (
    <section id="calculator" className="py-16 bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <Card className="overflow-hidden shadow-2xl border-0 bg-gradient-to-br from-blue-600 to-purple-600">
            <CardHeader className="text-center text-white relative overflow-hidden pb-8">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="relative z-10">
                <h2 className="text-3xl md:text-4xl font-bold mb-3">
                  Professional Age Calculator
                </h2>

                {/* Simple Mode Toggle - Readdy Style */}
                <div className="flex items-center justify-center gap-2 mb-4">
                  <button
                    onClick={() => setMode('standard')}
                    className={`px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      mode === 'standard'
                        ? 'bg-white text-blue-600 shadow-sm'
                        : 'border border-white/30 text-white hover:border-white/50'
                    }`}
                  >
                    Standard
                  </button>
                  <button
                    onClick={() => setMode('preemie')}
                    className={`px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      mode === 'preemie'
                        ? 'bg-white text-pink-600 shadow-sm'
                        : 'border border-white/30 text-white hover:border-white/50'
                    }`}
                  >
                    Preemie
                  </button>
                </div>

                {/* Current Mode Description */}
                <p className="text-white/80 text-sm max-w-lg mx-auto">
                  {mode === 'standard' ? 'Standard chronological age calculation' : 'Corrected age calculation for premature babies'}
                </p>
              </div>
            </CardHeader>

            <CardContent className="p-8 md:p-12">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                {/* Birth Date Input */}
                <div className="group">
                  <label htmlFor="birthDate" className="block text-lg font-semibold text-gray-800 mb-4">
                    出生日期
                  </label>
                  <div className="relative">
                    <input
                      type="date"
                      id="birthDate"
                      value={birthDate}
                      onChange={(e) => setBirthDate(e.target.value)}
                      placeholder="YYYY-MM-DD"
                      className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl text-gray-700 text-lg font-medium focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 bg-gray-50 hover:bg-white group-hover:border-blue-300 date-input-flexible"
                      required
                      lang="en-US"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                        <Calendar className="w-5 h-5 text-blue-600" />
                      </div>
                    </div>
                  </div>
                  {!birthDate && (
                    <p className="text-sm text-gray-500 mt-2">Please select the birth date</p>
                  )}
                </div>

                {/* Assessment Date Input */}
                <div className="group">
                  <label htmlFor="testDate" className="block text-lg font-semibold text-gray-800 mb-4">
                    {mode === 'professional' ? "Assessment Date" :
                     mode === 'slp' ? "Evaluation Date" :
                     mode === 'psychology' ? "Testing Date" : "Age as of Date"}
                  </label>
                  <div className="relative">
                    <input
                      type="date"
                      id="testDate"
                      value={isClient ? testDate : ''}
                      onChange={(e) => setTestDate(e.target.value)}
                      placeholder="YYYY-MM-DD"
                      className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl text-gray-700 text-lg font-medium focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 bg-gray-50 hover:bg-white group-hover:border-blue-300 date-input-flexible"
                      lang="en-US"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                        <Calendar className="w-5 h-5 text-green-600" />
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">Calculate age as of this specific date</p>
                </div>
              </div>

              {/* Gestational Age Fields for Preemie Mode */}
              {mode === 'preemie' && (
                <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-3xl p-8 mb-8 border border-pink-200">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-pink-500 rounded-2xl flex items-center justify-center mr-4">
                      <Baby className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">Gestational Age Information</h3>
                      <p className="text-gray-600">Required for corrected age calculation</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Gestational Weeks
                      </label>
                      <input
                        type="number"
                        value={gestationalWeeks}
                        onChange={(e) => setGestationalWeeks(e.target.value)}
                        placeholder="e.g., 32"
                        min="20"
                        max="42"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Gestational Days
                      </label>
                      <input
                        type="number"
                        value={gestationalDays}
                        onChange={(e) => setGestationalDays(e.target.value)}
                        placeholder="e.g., 3"
                        min="0"
                        max="6"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Calculate Button */}
              <div className="text-center mb-8">
                <button
                  onClick={calculateAge}
                  disabled={!birthDate || !testDate}
                  className={`inline-flex items-center px-12 py-4 rounded-2xl text-lg font-bold transition-all duration-300 transform hover:scale-105 ${
                    !birthDate || !testDate
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : `bg-gradient-to-r ${getGradientClasses(modes[mode].color)} text-white shadow-lg hover:shadow-xl`
                  }`}
                >
                  <Calculator className="w-6 h-6 mr-3" />
                  Calculate Age
                </button>
              </div>

              {/* Results Section */}
              {result ? (
                <div className="space-y-6">
                  {/* Main Result */}
                  <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-100">
                    <div className="text-center mb-6">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">
                        Chronological Age Result
                      </h3>
                      <div className="text-4xl md:text-5xl font-bold text-blue-600 mb-4">
                        {result.years} years, {result.months} months, {result.days} days
                      </div>
                      <div className="text-lg text-gray-600">
                        Professional Format: <span className="font-mono font-bold">{formatAgeForProfessional(result)}</span>
                      </div>
                    </div>

                    {/* Additional Information */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="text-center p-4 bg-blue-50 rounded-xl">
                        <div className="text-2xl font-bold text-blue-600">{result.totalDays}</div>
                        <div className="text-sm text-gray-600">Total Days</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-xl">
                        <div className="text-2xl font-bold text-green-600">{result.totalWeeks}</div>
                        <div className="text-sm text-gray-600">Total Weeks</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-xl">
                        <div className="text-2xl font-bold text-purple-600">{Math.floor(result.totalHours / 24 / 30.44)}</div>
                        <div className="text-sm text-gray-600">Total Months</div>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-xl">
                        <div className="text-2xl font-bold text-orange-600">{result.years}</div>
                        <div className="text-sm text-gray-600">Total Years</div>
                      </div>
                    </div>

                    {/* Copy Button */}
                    <div className="flex justify-center space-x-4">
                      <button
                        onClick={() => copyToClipboard(formatResultForCopy())}
                        className="inline-flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-colors"
                      >
                        {copied ? <Check className="w-5 h-5 mr-2" /> : <Copy className="w-5 h-5 mr-2" />}
                        {copied ? 'Copied!' : 'Copy Result'}
                      </button>
                    </div>
                  </div>

                  {/* Corrected Age for Preemie Mode */}
                  {correctedResult && mode === 'preemie' && (
                    <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-3xl p-8 border border-pink-200">
                      <div className="text-center mb-6">
                        <h3 className="text-2xl font-bold text-gray-900 mb-2">
                          Corrected Age Result
                        </h3>
                        <div className="text-3xl md:text-4xl font-bold text-pink-600 mb-4">
                          {correctedResult.years} years, {correctedResult.months} months, {correctedResult.days} days
                        </div>
                        <div className="text-lg text-gray-600">
                          Professional Format: <span className="font-mono font-bold">{formatAgeForProfessional(correctedResult)}</span>
                        </div>
                      </div>

                      <div className="bg-white/50 rounded-xl p-4 text-center">
                        <p className="text-sm text-gray-600">
                          Gestational Age: {gestationalWeeks} weeks {gestationalDays || 0} days
                          <br />
                          Adjustment: {Math.max(0, 40 - (parseInt(gestationalWeeks) + (gestationalDays ? parseInt(gestationalDays) / 7 : 0)))} weeks premature
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Professional Tips */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-3xl p-6 border border-blue-200">
                    <h4 className="font-bold text-gray-900 mb-3">Professional Tips for {modes[mode].title}:</h4>
                    <div className="space-y-2 text-sm text-gray-700">
                      {mode === 'professional' && (
                        <>
                          <p>• Use assessment date for standardized test administration</p>
                          <p>• Y;M;D format is required for most professional reports</p>
                          <p>• Document both birth date and assessment date in your records</p>
                        </>
                      )}
                      {mode === 'slp' && (
                        <>
                          <p>• Consider developmental milestones when interpreting results</p>
                          <p>• Use evaluation date for CELF-5, PPVT-5, and other assessments</p>
                          <p>• Track progress over multiple evaluation sessions</p>
                        </>
                      )}
                      {mode === 'psychology' && (
                        <>
                          <p>• Essential for IEP and 504 plan evaluations</p>
                          <p>• Required for WISC-V, WAIS-IV, and other cognitive assessments</p>
                          <p>• Consider grade-level expectations in your analysis</p>
                        </>
                      )}
                      {mode === 'preemie' && (
                        <>
                          <p>• Use corrected age for developmental assessments until age 2</p>
                          <p>• Both chronological and corrected ages may be relevant</p>
                          <p>• Consult with neonatologist for complex cases</p>
                        </>
                      )}
                      {mode === 'general' && (
                        <>
                          <p>• Perfect for birthday planning and milestone tracking</p>
                          <p>• Calculate age for any specific date in the past or future</p>
                          <p>• Useful for legal documents and applications</p>
                        </>
                      )}
                      {mode === 'standard' && (
                        <>
                          <p>• Precise calculation down to the day</p>
                          <p>• Handles leap years and varying month lengths</p>
                          <p>• Suitable for most general purposes</p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-3xl p-12 text-center shadow-lg border border-gray-100">
                  <Calculator className="w-16 h-16 text-gray-400 mx-auto mb-6" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Ready to Calculate</h3>
                  <p className="text-gray-600">
                    Enter birth date and assessment date to calculate chronological age with professional accuracy
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
