import { Star, <PERSON>, Brain, GraduationCap, Quote } from "lucide-react";

export function UserTestimonials() {
  const testimonials = [
    {
      name: "Dr. <PERSON>",
      role: "Speech-Language Pathologist",
      icon: Users,
      text: "Essential for my CELF-5 and PPVT-5 assessments. Saves hours each week.",
      color: "green"
    },
    {
      name: "Dr. <PERSON>",
      role: "School Psychologist",
      icon: <PERSON>,
      text: "Perfect Y;M;D format for IEP reports. Meets all our district standards.",
      color: "purple"
    },
    {
      name: "<PERSON>, M.<PERSON>.",
      role: "Assessment Coordinator",
      icon: GraduationCap,
      text: "Standardized across all our assessment teams. Reliable and fast.",
      color: "blue"
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Trusted by Professionals Nationwide
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              See what assessment professionals are saying about our chronological age calculator
            </p>
            

          </div>

          {/* Testimonials Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {testimonials.map((testimonial, index) => {
              const Icon = testimonial.icon;
              return (
                <div key={index} className="bg-gray-50 rounded-xl p-6">
                  <p className="text-gray-700 mb-4 leading-relaxed">
                    "{testimonial.text}"
                  </p>

                  <div className="flex items-center">
                    <div className={`w-10 h-10 bg-${testimonial.color}-100 rounded-full flex items-center justify-center mr-3`}>
                      <Icon className={`w-5 h-5 text-${testimonial.color}-600`} />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
