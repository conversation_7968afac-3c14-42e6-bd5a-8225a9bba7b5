import { Shield, Lock, Clock } from "lucide-react";

export function Hero() {
  return (
    <section className="relative py-8 md:py-12 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-blue-100 z-0">
        <div className="absolute inset-0 opacity-10 bg-cover bg-center"></div>
      </div>
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-3">
            <span className="inline-block px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-xs font-medium">
              Professional Medical & Educational Tool
            </span>
          </div>
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-3">
            Chronological Age Calculator
          </h1>
          <p className="text-base md:text-lg text-gray-700 mb-4 max-w-2xl mx-auto leading-relaxed">
            Professional chronological age calculator for healthcare professionals, educators, and researchers.
            Calculate precise age in years, months, and days with medical-grade accuracy for assessments,
            developmental evaluations, and educational planning.
          </p>
          <div className="flex flex-wrap justify-center gap-3 text-sm">
            <div className="flex items-center text-gray-600 bg-white/50 px-3 py-1.5 rounded-lg">
              <Shield className="w-4 h-4 text-blue-600 mr-1.5" />
              <span>100% Accurate</span>
            </div>
            <div className="flex items-center text-gray-600 bg-white/50 px-3 py-1.5 rounded-lg">
              <Lock className="w-4 h-4 text-blue-600 mr-1.5" />
              <span>Privacy Protected</span>
            </div>
            <div className="flex items-center text-gray-600 bg-white/50 px-3 py-1.5 rounded-lg">
              <Clock className="w-4 h-4 text-blue-600 mr-1.5" />
              <span>Real-time Results</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
