"use client";

import { useState } from "react";
import { ChevronDown, ChevronUp, HelpCircle } from "lucide-react";

export function ProfessionalFaq() {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: "How accurate is the chronological age calculation?",
      answer: "Our calculator provides medical-grade accuracy, correctly handling leap years and varying month lengths."
    },
    {
      question: "Is this calculator HIPAA compliant?",
      answer: "Yes, all calculations are performed locally in your browser. No personal data is stored or transmitted."
    },
    {
      question: "Does this work with CELF-5, PPVT-5, and other assessments?",
      answer: "Absolutely. Our calculator provides age calculations in the exact formats required for standardized tests."
    },
    {
      question: "Can this replace <PERSON>'s discontinued age calculator?",
      answer: "Yes, our calculator was designed to meet and exceed <PERSON>'s standards with additional professional features."
    }
  ];

  const toggleQuestion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Professional answers to common questions about chronological age calculations
            </p>
          </div>

          {/* FAQ List */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 divide-y divide-gray-100">
            {faqs.map((faq, index) => {
              const isOpen = openIndex === index;

              return (
                <div key={index}>
                  <button
                    onClick={() => toggleQuestion(index)}
                    className="w-full px-6 py-4 text-left hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-gray-900 pr-4">
                        {faq.question}
                      </h4>
                      {isOpen ? (
                        <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                      )}
                    </div>
                  </button>

                  {isOpen && (
                    <div className="px-6 pb-4">
                      <p className="text-gray-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
