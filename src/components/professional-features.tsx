import { 
  Calculator, 
  Shield, 
  Clock, 
  CheckCircle, 
  Smartphone, 
  FileText, 
  Users, 
  Zap 
} from "lucide-react";

export function ProfessionalFeatures() {
  const features = [
    {
      icon: Calculator,
      title: "Medical-Grade Accuracy",
      description: "Precise calculations for professional assessments",
      color: "blue"
    },
    {
      icon: FileText,
      title: "Professional Formats",
      description: "Y;M;D output ready for reports and evaluations",
      color: "green"
    },
    {
      icon: Shield,
      title: "Privacy & Security",
      description: "All calculations performed locally in your browser",
      color: "red"
    },
    {
      icon: Smartphone,
      title: "Mobile Optimized",
      description: "Works seamlessly on all devices for field assessments",
      color: "orange"
    }
  ];

  return (
    <section id="features" className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Professional-Grade Features
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Built specifically for healthcare professionals, educators, and assessment specialists 
              who require the highest levels of accuracy and reliability in their work.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div 
                  key={index}
                  className="bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-shadow duration-300 border border-gray-100"
                >
                  <div className={`w-12 h-12 bg-${feature.color}-100 rounded-lg flex items-center justify-center mb-4`}>
                    <Icon className={`w-6 h-6 text-${feature.color}-600`} />
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">
                    {feature.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
