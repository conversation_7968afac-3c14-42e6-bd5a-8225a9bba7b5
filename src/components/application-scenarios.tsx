import { Stethoscope, GraduationCap, Scale } from "lucide-react";

export function ApplicationScenarios() {
  return (
    <section className="py-8 md:py-12 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
              Professional Applications
            </h2>
            <p className="text-base text-gray-600 max-w-2xl mx-auto">
              Chronological age calculation is essential across multiple professional fields,
              each with specific requirements for precision and accuracy.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Medical Applications */}
            <div className="bg-gray-50 rounded-lg p-8 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center mb-6">
                <Stethoscope className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Medical Applications</h3>
              <p className="text-gray-700 mb-6">
                Essential for accurate medical assessments, treatment planning, and 
                developmental tracking in pediatric care.
              </p>
              
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900 text-sm">Key Uses:</h4>
                <ul className="text-gray-600 text-sm space-y-2">
                  <li>• Pediatric growth chart assessments</li>
                  <li>• Developmental milestone tracking</li>
                  <li>• Age-specific medication dosing</li>
                  <li>• Vaccination schedule planning</li>
                  <li>• Clinical research eligibility</li>
                  <li>• Geriatric care assessments</li>
                </ul>
              </div>
              
              <div className="mt-6 p-4 bg-red-50 rounded border-l-4 border-red-200">
                <p className="text-red-800 text-sm">
                  <strong>Critical Accuracy:</strong> Medical decisions often depend on 
                  precise age calculations, especially in pediatric and geriatric care.
                </p>
              </div>
            </div>
            
            {/* Educational Applications */}
            <div className="bg-gray-50 rounded-lg p-8 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                <GraduationCap className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Educational Use</h3>
              <p className="text-gray-700 mb-6">
                Critical for school enrollment, grade placement, and standardized 
                testing age calculations.
              </p>
              
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900 text-sm">Key Uses:</h4>
                <ul className="text-gray-600 text-sm space-y-2">
                  <li>• School admission requirements</li>
                  <li>• Grade level placement decisions</li>
                  <li>• Standardized test eligibility</li>
                  <li>• Special education assessments</li>
                  <li>• Academic research studies</li>
                  <li>• Sports team age verification</li>
                </ul>
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 rounded border-l-4 border-blue-200">
                <p className="text-blue-800 text-sm">
                  <strong>Educational Impact:</strong> Accurate age calculation ensures 
                  appropriate academic placement and fair assessment opportunities.
                </p>
              </div>
            </div>
            
            {/* Legal & Administrative */}
            <div className="bg-gray-50 rounded-lg p-8 hover:shadow-lg transition-shadow">
              <div className="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                <Scale className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Legal & Administrative</h3>
              <p className="text-gray-700 mb-6">
                Required for legal documentation, identification, and age-specific 
                administrative procedures.
              </p>
              
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-900 text-sm">Key Uses:</h4>
                <ul className="text-gray-600 text-sm space-y-2">
                  <li>• Legal age verification</li>
                  <li>• Insurance policy calculations</li>
                  <li>• Retirement benefit planning</li>
                  <li>• Court proceedings</li>
                  <li>• Immigration documentation</li>
                  <li>• Employment eligibility</li>
                </ul>
              </div>
              
              <div className="mt-6 p-4 bg-purple-50 rounded border-l-4 border-purple-200">
                <p className="text-purple-800 text-sm">
                  <strong>Legal Precision:</strong> Legal and administrative decisions 
                  require exact age calculations to ensure compliance and fairness.
                </p>
              </div>
            </div>
          </div>
          
          <div className="mt-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8">
            <div className="text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Professional Standards Compliance
              </h3>
              <p className="text-gray-700 mb-6 max-w-3xl mx-auto">
                Our calculator meets the precision requirements used by healthcare institutions, 
                educational systems, and legal organizations worldwide. The same algorithms 
                trusted by professionals are now available for your personal use.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">99.9%</div>
                  <div className="text-sm text-gray-600">Calculation Accuracy</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">ISO 8601</div>
                  <div className="text-sm text-gray-600">Date Standard Compliance</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">24/7</div>
                  <div className="text-sm text-gray-600">Available Access</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
