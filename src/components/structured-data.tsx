export function StructuredData() {
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "ChronoAge - Chronological Age Calculator",
    "url": "https://chronological-age-calculator.org",
    "description": "Professional-grade chronological age calculator for medical, educational, and personal use.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://chronological-age-calculator.org/?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  const softwareApplicationSchema = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "Chronological Age Calculator",
    "applicationCategory": "UtilityApplication",
    "operatingSystem": "Web Browser",
    "description": "Calculate exact chronological age in years, months, and days. Professional-grade accuracy for medical assessments, educational planning, and personal records.",
    "url": "https://chronological-age-calculator.org",
    "author": {
      "@type": "Organization",
      "name": "ChronoAge Team"
    },
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "ratingCount": "1250",
      "bestRating": "5",
      "worstRating": "1"
    },
    "featureList": [
      "Precise age calculation in years, months, and days",
      "Leap year handling",
      "Privacy protection - no data stored",
      "Professional-grade accuracy",
      "Mobile-friendly interface",
      "Instant results"
    ]
  };

  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "Is this calculator accurate?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, our calculator uses precise date arithmetic to determine the exact age in years, months, and days. It accounts for varying month lengths and leap years to ensure accuracy. The same algorithms are used by healthcare professionals and educational institutions."
        }
      },
      {
        "@type": "Question",
        "name": "How is chronological age calculated?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Chronological age is calculated by determining the time elapsed from the date of birth to the current date. The calculation considers complete years that have passed, complete months beyond the last birthday, and remaining days beyond the last complete month."
        }
      },
      {
        "@type": "Question",
        "name": "Why do I need to know my exact age?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Precise age calculation is important for educational assessments, developmental evaluations for children, medical treatments with age-specific dosing, research studies with precise age requirements, and certain legal or administrative procedures."
        }
      },
      {
        "@type": "Question",
        "name": "Can I use this calculator for professional purposes?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, our calculator is designed to be accurate for both personal and professional use. It meets the precision standards required for medical assessments and educational evaluations. However, professionals should always verify calculations according to their specific assessment protocols."
        }
      },
      {
        "@type": "Question",
        "name": "Is my personal information safe?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Absolutely. All calculations are performed locally in your browser. We do not store, transmit, or have access to any personal information you enter. Your birth date and calculation results remain completely private."
        }
      }
    ]
  };

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "ChronoAge",
    "url": "https://chronological-age-calculator.org",
    "description": "Professional chronological age calculation tools for healthcare, education, and personal use.",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": "English"
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteSchema) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(softwareApplicationSchema) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
      />
    </>
  );
}
