import { Shield, Users, Clock, CheckCircle, Calculator, Mail } from "lucide-react";

export function SupportCommitment() {
  const commitments = [
    {
      title: "Long-term Availability",
      icon: Shield,
      description: "Unlike <PERSON>'s discontinuation, we guarantee long-term service availability",
      details: [
        "Minimum 10-year service commitment",
        "No sudden discontinuation policy",
        "Advance notice for any major changes",
        "Migration assistance if ever needed"
      ]
    },
    {
      title: "Professional Support",
      icon: Users,
      description: "Dedicated support team with assessment expertise",
      details: [
        "Assessment professional consultation",
        "Technical support within 24 hours",
        "Training and onboarding assistance",
        "Custom feature development"
      ]
    },
    {
      title: "Continuous Development",
      icon: Clock,
      description: "Regular updates and feature enhancements based on user feedback",
      details: [
        "Monthly feature updates",
        "User-requested enhancements",
        "Bug fixes within 48 hours",
        "Performance optimizations"
      ]
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Our Commitment to Assessment Professionals
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We understand the impact of Pearson's discontinuation and are committed to providing 
              a reliable, long-term solution for the assessment community.
            </p>
          </div>

          {/* Commitments */}
          <div className="space-y-8 mb-16">
            {commitments.map((commitment, index) => {
              const Icon = commitment.icon;
              return (
                <div key={index} className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div className="text-center lg:text-left">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto lg:mx-0 mb-4">
                        <Icon className="w-8 h-8 text-blue-600" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3">{commitment.title}</h3>
                      <p className="text-gray-600">{commitment.description}</p>
                    </div>

                    <div className="lg:col-span-2">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {commitment.details.map((detail, idx) => (
                          <div key={idx} className="flex items-start">
                            <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{detail}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Contact and Support */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 rounded-xl p-8 border border-blue-200">
              <div className="text-center mb-6">
                <Calculator className="w-16 h-16 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Start Using Today</h3>
                <p className="text-gray-600">
                  Begin your migration from Pearson's calculator immediately
                </p>
              </div>
              
              <div className="space-y-4">
                <a 
                  href="/#calculator"
                  className="block w-full text-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Open Professional Calculator
                </a>
                <a 
                  href="/professional-guide"
                  className="block w-full text-center px-6 py-3 border border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-colors"
                >
                  View Professional Guide
                </a>
              </div>
            </div>

            <div className="bg-green-50 rounded-xl p-8 border border-green-200">
              <div className="text-center mb-6">
                <Mail className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">Need Support?</h3>
                <p className="text-gray-600">
                  Our team is here to help with your migration and any questions
                </p>
              </div>
              
              <div className="space-y-4">
                <a 
                  href="mailto:<EMAIL>"
                  className="block w-full text-center px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition-colors"
                >
                  Contact Support Team
                </a>
                <a 
                  href="#migration-guide"
                  className="block w-full text-center px-6 py-3 border border-green-600 text-green-600 font-semibold rounded-lg hover:bg-green-50 transition-colors"
                >
                  View Migration Guide
                </a>
              </div>
            </div>
          </div>

          {/* Final CTA */}
          <div className="mt-16 bg-gray-900 rounded-2xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-4">
              Don't Let Pearson's Discontinuation Disrupt Your Practice
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Join thousands of assessment professionals who have successfully migrated to our 
              reliable, enhanced alternative. Experience the same accuracy you trusted, 
              plus professional features Pearson never offered.
            </p>
            <a 
              href="/#calculator"
              className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Calculator className="w-5 h-5 mr-2" />
              Start Your Migration Now
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
