import { Shield, Zap, Smartphone, Database } from "lucide-react";

export function TechnicalSpecs() {
  const specs = [
    {
      category: "Accuracy & Precision",
      icon: Shield,
      color: "blue",
      details: [
        "99.99% calculation accuracy",
        "Medical-grade precision standards",
        "Handles all edge cases (leap years, month variations)",
        "Validated against known test cases",
        "Zero tolerance for calculation errors"
      ]
    },
    {
      category: "Performance",
      icon: Zap,
      color: "green",
      details: [
        "Sub-second calculation response time",
        "Optimized for high-volume usage",
        "Efficient memory management",
        "No server dependencies for calculations",
        "Works offline after initial load"
      ]
    },
    {
      category: "Compatibility",
      icon: Smartphone,
      color: "purple",
      details: [
        "All modern web browsers supported",
        "Mobile and tablet optimized",
        "Responsive design for all screen sizes",
        "Touch-friendly interface",
        "Keyboard navigation support"
      ]
    },
    {
      category: "Privacy & Security",
      icon: Database,
      color: "orange",
      details: [
        "No personal data storage",
        "Local calculations only",
        "HIPAA-compliant design",
        "No tracking or analytics",
        "Secure HTTPS connection"
      ]
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Technical Specifications
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Built with professional-grade standards to ensure reliability, 
              accuracy, and security for assessment professionals.
            </p>
          </div>

          {/* Technical Specs Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {specs.map((spec, index) => {
              const Icon = spec.icon;
              return (
                <div key={index} className={`bg-${spec.color}-50 rounded-xl p-8 border border-${spec.color}-200`}>
                  <div className="flex items-center mb-6">
                    <div className={`w-12 h-12 bg-${spec.color}-100 rounded-full flex items-center justify-center mr-4`}>
                      <Icon className={`w-6 h-6 text-${spec.color}-600`} />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">{spec.category}</h3>
                  </div>

                  <div className="space-y-3">
                    {spec.details.map((detail, idx) => (
                      <div key={idx} className="flex items-start">
                        <div className={`w-2 h-2 bg-${spec.color}-500 rounded-full mr-3 mt-2 flex-shrink-0`}></div>
                        <span className="text-gray-700 text-sm">{detail}</span>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Comparison with Pearson */}
          <div className="mt-16 bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              Technical Comparison with Pearson
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">Same</div>
                <div className="font-semibold text-gray-900 mb-2">Calculation Accuracy</div>
                <p className="text-gray-600 text-sm">
                  Maintains the same 99.99% precision that professionals trusted in Pearson's calculator
                </p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">Better</div>
                <div className="font-semibold text-gray-900 mb-2">Performance & Features</div>
                <p className="text-gray-600 text-sm">
                  Faster calculations, mobile optimization, and enhanced professional features
                </p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">Enhanced</div>
                <div className="font-semibold text-gray-900 mb-2">Privacy & Security</div>
                <p className="text-gray-600 text-sm">
                  Improved privacy protection with local calculations and no data storage
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
