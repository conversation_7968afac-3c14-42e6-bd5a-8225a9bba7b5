import { CheckCircle, X, Calculator, Shield, Smartphone, Clock } from "lucide-react";

export function FeatureComparison() {
  const features = [
    {
      feature: "Medical-Grade Accuracy",
      pearson: true,
      ourSolution: true,
      generic: false,
      description: "99.99% precision with edge case handling"
    },
    {
      feature: "Professional Output Formats",
      pearson: true,
      ourSolution: true,
      generic: false,
      description: "Y;M;D and other assessment-specific formats"
    },
    {
      feature: "Test Date Flexibility",
      pearson: true,
      ourSolution: true,
      generic: false,
      description: "Calculate age for any assessment date"
    },
    {
      feature: "Mobile Optimization",
      pearson: false,
      ourSolution: true,
      generic: true,
      description: "Optimized for tablets and smartphones"
    },
    {
      feature: "Multiple Professional Modes",
      pearson: false,
      ourSolution: true,
      generic: false,
      description: "SLP, Psychology, Assessment, General modes"
    },
    {
      feature: "Assessment Tool Integration",
      pearson: false,
      ourSolution: true,
      generic: false,
      description: "WISC-V, CELF-5, PPVT-5 specific features"
    },
    {
      feature: "Privacy Protection",
      pearson: true,
      ourSolution: true,
      generic: false,
      description: "Local calculations, no data storage"
    },
    {
      feature: "Professional Support",
      pearson: false,
      ourSolution: true,
      generic: false,
      description: "Dedicated support for assessment professionals"
    },
    {
      feature: "Ongoing Development",
      pearson: false,
      ourSolution: true,
      generic: false,
      description: "Regular updates and feature enhancements"
    },
    {
      feature: "Service Reliability",
      pearson: false,
      ourSolution: true,
      generic: true,
      description: "Guaranteed long-term availability"
    }
  ];

  return (
    <section id="feature-comparison" className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Comprehensive Feature Comparison
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              See how our professional alternative compares to Pearson's original calculator 
              and generic online tools.
            </p>
          </div>

          {/* Comparison Table */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50 border-b border-gray-200">
                    <th className="text-left py-6 px-6 font-semibold text-gray-900">Feature</th>
                    <th className="text-center py-6 px-4 font-semibold text-red-600">
                      Pearson Calculator<br />
                      <span className="text-xs text-red-500">(Discontinued)</span>
                    </th>
                    <th className="text-center py-6 px-4 font-semibold text-blue-600">
                      Our Professional Solution<br />
                      <span className="text-xs text-blue-500">(Active & Enhanced)</span>
                    </th>
                    <th className="text-center py-6 px-4 font-semibold text-gray-600">
                      Generic Calculators<br />
                      <span className="text-xs text-gray-500">(Various Online Tools)</span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {features.map((feature, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-6">
                        <div>
                          <div className="font-medium text-gray-900">{feature.feature}</div>
                          <div className="text-sm text-gray-600">{feature.description}</div>
                        </div>
                      </td>
                      <td className="py-4 px-4 text-center">
                        {feature.pearson ? (
                          <CheckCircle className="w-6 h-6 text-red-500 mx-auto" />
                        ) : (
                          <X className="w-6 h-6 text-gray-400 mx-auto" />
                        )}
                      </td>
                      <td className="py-4 px-4 text-center">
                        {feature.ourSolution ? (
                          <CheckCircle className="w-6 h-6 text-blue-600 mx-auto" />
                        ) : (
                          <X className="w-6 h-6 text-gray-400 mx-auto" />
                        )}
                      </td>
                      <td className="py-4 px-4 text-center">
                        {feature.generic ? (
                          <CheckCircle className="w-6 h-6 text-gray-500 mx-auto" />
                        ) : (
                          <X className="w-6 h-6 text-gray-400 mx-auto" />
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Key Advantages */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-blue-50 rounded-xl p-6 border border-blue-200 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calculator className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="font-bold text-blue-900 mb-2">Enhanced Functionality</h3>
              <p className="text-blue-800 text-sm">
                All Pearson features plus professional enhancements
              </p>
            </div>

            <div className="bg-green-50 rounded-xl p-6 border border-green-200 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="font-bold text-green-900 mb-2">Guaranteed Reliability</h3>
              <p className="text-green-800 text-sm">
                Long-term commitment with ongoing support
              </p>
            </div>

            <div className="bg-purple-50 rounded-xl p-6 border border-purple-200 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Smartphone className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="font-bold text-purple-900 mb-2">Modern Interface</h3>
              <p className="text-purple-800 text-sm">
                Mobile-optimized with intuitive design
              </p>
            </div>

            <div className="bg-orange-50 rounded-xl p-6 border border-orange-200 text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="font-bold text-orange-900 mb-2">Always Available</h3>
              <p className="text-orange-800 text-sm">
                24/7 access without service interruptions
              </p>
            </div>
          </div>

          {/* Migration CTA */}
          <div className="mt-16 bg-blue-600 rounded-2xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-4">Ready to Upgrade from Pearson?</h3>
            <p className="text-blue-100 mb-6">
              Experience all the features you loved about Pearson's calculator, plus professional enhancements
            </p>
            <a 
              href="/#calculator"
              className="inline-flex items-center px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-colors"
            >
              <Calculator className="w-5 h-5 mr-2" />
              Try Professional Calculator
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
