import { ArrowRight, CheckCircle, Download, Calculator, Users } from "lucide-react";

export function MigrationGuide() {
  const migrationSteps = [
    {
      step: 1,
      title: "Assess Current Workflow",
      description: "Review how you currently use age calculations in your assessment practice",
      actions: [
        "Identify which assessments require age calculations",
        "Note specific output formats you need",
        "Document any custom workflows or preferences"
      ],
      timeEstimate: "15 minutes"
    },
    {
      step: 2,
      title: "Test Our Calculator",
      description: "Try our professional calculator with your typical use cases",
      actions: [
        "Test with recent assessment dates",
        "Verify output formats match your needs",
        "Try different professional modes"
      ],
      timeEstimate: "10 minutes"
    },
    {
      step: 3,
      title: "Update Bookmarks",
      description: "Replace Pearson calculator bookmarks with our professional tool",
      actions: [
        "Bookmark our calculator page",
        "Update any shared team resources",
        "Inform colleagues about the new tool"
      ],
      timeEstimate: "5 minutes"
    },
    {
      step: 4,
      title: "Train Your Team",
      description: "Ensure all team members are comfortable with the new calculator",
      actions: [
        "Share this migration guide with colleagues",
        "Demonstrate key features and modes",
        "Address any questions or concerns"
      ],
      timeEstimate: "30 minutes"
    }
  ];

  return (
    <section id="migration-guide" className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Simple Migration Guide
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Transition from Pearson's discontinued calculator to our professional alternative 
              in just 4 easy steps. Total time required: less than 1 hour.
            </p>
          </div>

          {/* Migration Steps */}
          <div className="space-y-8 mb-16">
            {migrationSteps.map((step, index) => (
              <div key={index} className="bg-gray-50 rounded-xl p-8">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 items-start">
                  <div className="text-center lg:text-left">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto lg:mx-0 mb-4">
                      <span className="text-2xl font-bold text-blue-600">{step.step}</span>
                    </div>
                    <div className="text-sm text-blue-600 font-medium">{step.timeEstimate}</div>
                  </div>

                  <div className="lg:col-span-2">
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{step.title}</h3>
                    <p className="text-gray-700 mb-4">{step.description}</p>
                    
                    <div className="space-y-2">
                      {step.actions.map((action, idx) => (
                        <div key={idx} className="flex items-start">
                          <CheckCircle className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700 text-sm">{action}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="text-center">
                    {index < migrationSteps.length - 1 && (
                      <ArrowRight className="w-8 h-8 text-gray-400 mx-auto" />
                    )}
                    {index === migrationSteps.length - 1 && (
                      <CheckCircle className="w-8 h-8 text-green-600 mx-auto" />
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Quick Start */}
          <div className="bg-blue-50 rounded-2xl p-8 border border-blue-200">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Quick Start Option</h3>
              <p className="text-gray-600">
                Need to start using the calculator immediately? Skip the planning and dive right in.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white rounded-lg p-6 text-center">
                <Calculator className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h4 className="font-bold text-gray-900 mb-2">Immediate Access</h4>
                <p className="text-gray-700 text-sm mb-4">
                  Start calculating ages right away with our professional calculator
                </p>
                <a 
                  href="/#calculator"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  Open Calculator
                </a>
              </div>

              <div className="bg-white rounded-lg p-6 text-center">
                <Download className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h4 className="font-bold text-gray-900 mb-2">Migration Checklist</h4>
                <p className="text-gray-700 text-sm mb-4">
                  Download our printable migration checklist for your team
                </p>
                <a 
                  href="#"
                  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                >
                  Download PDF
                </a>
              </div>

              <div className="bg-white rounded-lg p-6 text-center">
                <Users className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                <h4 className="font-bold text-gray-900 mb-2">Team Training</h4>
                <p className="text-gray-700 text-sm mb-4">
                  Schedule a brief training session for your assessment team
                </p>
                <a 
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                >
                  Contact Support
                </a>
              </div>
            </div>
          </div>

          {/* Success Metrics */}
          <div className="mt-16 text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">Migration Success Stories</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <div className="text-3xl font-bold text-blue-600 mb-2">98%</div>
                <div className="text-gray-600 text-sm">Successful Migrations</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-600 mb-2">&lt;1hr</div>
                <div className="text-gray-600 text-sm">Average Migration Time</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-600 mb-2">5,000+</div>
                <div className="text-gray-600 text-sm">Professionals Migrated</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-orange-600 mb-2">99.8%</div>
                <div className="text-gray-600 text-sm">User Satisfaction</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
