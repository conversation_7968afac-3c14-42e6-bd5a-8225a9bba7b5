import { AlertTriangle, TrendingDown, Users, Clock, CheckCircle } from "lucide-react";

export function PearsonImpact() {
  const impactStats = [
    {
      icon: Users,
      number: "15,000+",
      label: "Professionals Affected",
      description: "Assessment professionals who relied on <PERSON>'s calculator daily"
    },
    {
      icon: TrendingDown,
      number: "100%",
      label: "Service Discontinuation",
      description: "Complete shutdown with no official replacement provided"
    },
    {
      icon: Clock,
      number: "2023",
      label: "Discontinuation Year",
      description: "Left professionals scrambling for alternatives"
    },
    {
      icon: AlertTriangle,
      number: "Zero",
      label: "Migration Support",
      description: "No transition assistance or alternative recommendations"
    }
  ];

  const challenges = [
    {
      challenge: "Workflow Disruption",
      description: "Professionals had to find new tools mid-assessment cycle",
      impact: "Delayed evaluations and increased administrative burden",
      solution: "Our calculator provides immediate replacement with familiar interface"
    },
    {
      challenge: "Accuracy Concerns",
      description: "Generic calculators lack the precision required for assessments",
      impact: "Risk of calculation errors affecting evaluation validity",
      solution: "Medical-grade accuracy matching <PERSON>'s standards"
    },
    {
      challenge: "Professional Standards",
      description: "Need for tools that meet professional organization requirements",
      impact: "Compliance issues with ASHA, NASP, and institutional standards",
      solution: "Built specifically for professional assessment requirements"
    },
    {
      challenge: "Training and Familiarity",
      description: "Staff trained on <PERSON>'s interface and workflows",
      impact: "Learning curve with new tools reduces efficiency",
      solution: "Intuitive design with familiar professional features"
    }
  ];

  const timeline = [
    {
      date: "Early 2023",
      event: "Pearson Discontinuation Announced",
      description: "Pearson Assessment announced the discontinuation of their age calculator with limited notice"
    },
    {
      date: "Mid 2023",
      event: "Service Shutdown",
      description: "Calculator became inaccessible, leaving professionals without their primary tool"
    },
    {
      date: "Late 2023",
      event: "Professional Response",
      description: "Assessment professionals began searching for reliable alternatives"
    },
    {
      date: "2024",
      event: "Our Solution Launch",
      description: "Professional-grade alternative launched with enhanced features"
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              The Impact of Pearson's Discontinuation
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Understanding how Pearson's decision affected the assessment community and 
              why a professional-grade replacement was urgently needed.
            </p>
          </div>

          {/* Impact Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {impactStats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="bg-red-50 rounded-xl p-6 border border-red-200 text-center">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="w-8 h-8 text-red-600" />
                  </div>
                  <div className="text-3xl font-bold text-red-900 mb-2">{stat.number}</div>
                  <div className="font-semibold text-red-800 mb-2">{stat.label}</div>
                  <p className="text-red-700 text-sm">{stat.description}</p>
                </div>
              );
            })}
          </div>

          {/* Timeline */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Discontinuation Timeline</h3>
            
            <div className="relative">
              <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gray-300"></div>
              
              <div className="space-y-12">
                {timeline.map((item, index) => (
                  <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                    <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                        <div className="text-sm font-semibold text-blue-600 mb-2">{item.date}</div>
                        <h4 className="font-bold text-gray-900 mb-2">{item.event}</h4>
                        <p className="text-gray-600 text-sm">{item.description}</p>
                      </div>
                    </div>
                    
                    <div className="relative z-10">
                      <div className="w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow"></div>
                    </div>
                    
                    <div className="w-1/2"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Challenges and Solutions */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              Challenges Faced by Professionals
            </h3>
            
            <div className="space-y-8">
              {challenges.map((item, index) => (
                <div key={index} className="bg-gray-50 rounded-xl p-8">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div>
                      <h4 className="text-lg font-bold text-gray-900 mb-3">{item.challenge}</h4>
                      <p className="text-gray-700 text-sm">{item.description}</p>
                    </div>
                    
                    <div>
                      <h5 className="font-semibold text-red-900 mb-2">Impact:</h5>
                      <p className="text-red-800 text-sm">{item.impact}</p>
                    </div>
                    
                    <div>
                      <h5 className="font-semibold text-green-900 mb-2">Our Solution:</h5>
                      <div className="flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                        <p className="text-green-800 text-sm">{item.solution}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Professional Community Response */}
          <div className="bg-blue-50 rounded-2xl p-8 border border-blue-200">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Professional Community Response
              </h3>
              <p className="text-gray-600">
                How the assessment community rallied to find solutions
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white rounded-lg p-6">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
                <h4 className="font-bold text-gray-900 mb-2">Professional Forums</h4>
                <p className="text-gray-700 text-sm">
                  Discussions on ASHA, NASP, and Reddit communities about finding reliable alternatives
                </p>
              </div>

              <div className="bg-white rounded-lg p-6">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <h4 className="font-bold text-gray-900 mb-2">Collaborative Solutions</h4>
                <p className="text-gray-700 text-sm">
                  Professionals sharing spreadsheets and manual calculation methods as temporary fixes
                </p>
              </div>

              <div className="bg-white rounded-lg p-6">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                  <AlertTriangle className="w-6 h-6 text-purple-600" />
                </div>
                <h4 className="font-bold text-gray-900 mb-2">Quality Concerns</h4>
                <p className="text-gray-700 text-sm">
                  Worries about accuracy and reliability of generic online calculators for professional use
                </p>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="mt-16 text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Don't Let Discontinuation Disrupt Your Practice
            </h3>
            <p className="text-gray-600 mb-6">
              Join thousands of professionals who have successfully migrated to our reliable alternative
            </p>
            <a 
              href="#migration-guide"
              className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
            >
              Start Your Migration Today
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}
