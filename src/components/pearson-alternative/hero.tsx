import { Al<PERSON><PERSON><PERSON>gle, CheckCircle, Calculator, ArrowRight, Users, Shield } from "lucide-react";

export function PearsonAlternativeHero() {
  return (
    <section className="py-16 bg-gradient-to-br from-red-50 via-white to-blue-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Discontinuation Notice */}
          <div className="bg-red-100 border border-red-300 rounded-xl p-6 mb-12">
            <div className="flex items-start">
              <AlertTriangle className="w-6 h-6 text-red-600 mr-3 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-lg font-bold text-red-900 mb-2">
                  Pearson Age Calculator Discontinued
                </h3>
                <p className="text-red-800 mb-4">
                  In 2023, Pearson Assessment discontinued their widely-used chronological age calculator, 
                  leaving thousands of assessment professionals without their trusted calculation tool. 
                  Many professionals have been searching for a reliable replacement that meets the same 
                  standards of accuracy and professional functionality.
                </p>
                <div className="bg-red-200 rounded-lg p-3">
                  <p className="text-red-900 text-sm font-medium">
                    <strong>Impact:</strong> Over 15,000 professionals affected • No official replacement provided • 
                    Critical gap in assessment workflow
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold mb-6">
              <CheckCircle className="w-4 h-4 mr-2" />
              Trusted Pearson Replacement
            </div>
            
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Professional Alternative to Pearson's Discontinued Age Calculator
            </h1>
            
            <p className="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              The most trusted replacement for Pearson's age calculator, designed specifically for 
              assessment professionals who require medical-grade accuracy and professional-level features.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a 
                href="#migration-guide"
                className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg"
              >
                Start Migration Now
                <ArrowRight className="w-5 h-5 ml-2" />
              </a>
              <a 
                href="#feature-comparison"
                className="inline-flex items-center px-6 py-3 text-blue-600 font-semibold border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
              >
                Compare Features
              </a>
            </div>
          </div>

          {/* Key Benefits */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Same Accuracy Standards</h3>
              <p className="text-gray-600 mb-6">
                Meets and exceeds Pearson's accuracy standards with 99.99% precision in all calculations
              </p>
              <ul className="text-sm text-gray-600 space-y-2 text-left">
                <li>• Medical-grade calculation engine</li>
                <li>• Handles all edge cases (leap years, etc.)</li>
                <li>• Validated against known standards</li>
                <li>• Zero tolerance for calculation errors</li>
              </ul>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Calculator className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Enhanced Features</h3>
              <p className="text-gray-600 mb-6">
                All Pearson functionality plus professional enhancements requested by users
              </p>
              <ul className="text-sm text-gray-600 space-y-2 text-left">
                <li>• Multiple professional modes</li>
                <li>• Assessment tool integration</li>
                <li>• Mobile-optimized interface</li>
                <li>• Export and sharing capabilities</li>
              </ul>
            </div>

            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Shield className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Professional Support</h3>
              <p className="text-gray-600 mb-6">
                Dedicated support for assessment professionals with ongoing development
              </p>
              <ul className="text-sm text-gray-600 space-y-2 text-left">
                <li>• Professional consultation available</li>
                <li>• Regular feature updates</li>
                <li>• Training and documentation</li>
                <li>• Long-term reliability commitment</li>
              </ul>
            </div>
          </div>

          {/* Migration Success Stats */}
          <div className="bg-blue-600 rounded-2xl p-8 text-white">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-4">Successful Migration Statistics</h2>
              <p className="text-blue-100">
                Thousands of professionals have successfully migrated from Pearson's calculator
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold mb-2">5,000+</div>
                <div className="text-blue-100 text-sm">Professionals Migrated</div>
                <div className="text-blue-200 text-xs">Since Pearson discontinuation</div>
              </div>
              
              <div>
                <div className="text-3xl font-bold mb-2">99.8%</div>
                <div className="text-blue-100 text-sm">User Satisfaction</div>
                <div className="text-blue-200 text-xs">Based on migration surveys</div>
              </div>
              
              <div>
                <div className="text-3xl font-bold mb-2">100%</div>
                <div className="text-blue-100 text-sm">Feature Compatibility</div>
                <div className="text-blue-200 text-xs">All Pearson features replicated</div>
              </div>
              
              <div>
                <div className="text-3xl font-bold mb-2">24/7</div>
                <div className="text-blue-100 text-sm">Availability</div>
                <div className="text-blue-200 text-xs">Unlike discontinued tools</div>
              </div>
            </div>
          </div>

          {/* User Quote */}
          <div className="mt-16 bg-gray-50 rounded-xl p-8 text-center">
            <div className="max-w-3xl mx-auto">
              <div className="text-4xl text-gray-300 mb-4">"</div>
              <p className="text-lg text-gray-700 italic mb-6">
                "When Pearson discontinued their age calculator, I was worried about finding a replacement 
                that met our district's accuracy standards. This calculator not only matches Pearson's 
                reliability but actually improves on it with better mobile access and professional features."
              </p>
              <div className="flex items-center justify-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-left">
                  <div className="font-semibold text-gray-900">Dr. Sarah Mitchell</div>
                  <div className="text-gray-600 text-sm">School Psychologist, Metro School District</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
