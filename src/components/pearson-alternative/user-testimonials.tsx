import { Star, <PERSON>, Brain, GraduationCap } from "lucide-react";

export function UserTestimonials() {
  const testimonials = [
    {
      name: "Dr. <PERSON>",
      role: "School Psychologist",
      organization: "Metro School District",
      icon: Brain,
      rating: 5,
      quote: "When Pearson discontinued their calculator, I was worried about finding a replacement that met our district's accuracy standards. This calculator not only matches Pearson's reliability but actually improves on it with better mobile access and professional features.",
      highlight: "Exceeds Pearson's reliability"
    },
    {
      name: "<PERSON>, M.S., CCC-SLP",
      role: "Speech-Language Pathologist",
      organization: "Children's Therapy Center",
      icon: Users,
      rating: 5,
      quote: "The SLP mode is exactly what I needed. It integrates perfectly with my CELF-5 and PPVT-5 assessments, and the Y;M format is exactly what I was used to with <PERSON>. The migration was seamless.",
      highlight: "Perfect SLP integration"
    },
    {
      name: "Dr. <PERSON>",
      role: "Clinical Psychologist",
      organization: "University Assessment Clinic",
      icon: GraduationCap,
      rating: 5,
      quote: "As someone who conducts research requiring precise age calculations, I appreciate the medical-grade accuracy. The fact that it handles edge cases like leap years automatically gives me confidence in my data.",
      highlight: "Medical-grade accuracy"
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              What Professionals Are Saying
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Hear from assessment professionals who have successfully migrated 
              from Pearson's calculator to our professional alternative.
            </p>
          </div>

          {/* Testimonials */}
          <div className="space-y-8">
            {testimonials.map((testimonial, index) => {
              const Icon = testimonial.icon;
              return (
                <div key={index} className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                    <div className="text-center lg:text-left">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto lg:mx-0 mb-4">
                        <Icon className="w-8 h-8 text-blue-600" />
                      </div>
                      <h4 className="font-bold text-gray-900">{testimonial.name}</h4>
                      <p className="text-gray-600 text-sm">{testimonial.role}</p>
                      <p className="text-gray-500 text-xs">{testimonial.organization}</p>
                      
                      <div className="flex justify-center lg:justify-start mt-3">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                      </div>
                    </div>

                    <div className="lg:col-span-3">
                      <div className="mb-4">
                        <span className="inline-block px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                          {testimonial.highlight}
                        </span>
                      </div>
                      
                      <blockquote className="text-gray-700 italic text-lg leading-relaxed">
                        "{testimonial.quote}"
                      </blockquote>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Stats */}
          <div className="mt-16 bg-blue-600 rounded-2xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-8">Migration Success Statistics</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <div className="text-3xl font-bold mb-2">5,000+</div>
                <div className="text-blue-100 text-sm">Professionals Migrated</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">99.8%</div>
                <div className="text-blue-100 text-sm">User Satisfaction</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">4.9/5</div>
                <div className="text-blue-100 text-sm">Average Rating</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">100%</div>
                <div className="text-blue-100 text-sm">Would Recommend</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
