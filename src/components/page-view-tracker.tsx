"use client";

import { useEffect } from 'react';
import { trackPageView } from '@/components/google-analytics';

interface PageViewTrackerProps {
  pageName: string;
  category?: string;
}

export function PageViewTracker({ pageName, category = 'page_view' }: PageViewTrackerProps) {
  useEffect(() => {
    // Track page view when component mounts
    trackPageView(window.location.pathname);
    
    // Track custom page view event
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'page_view', {
        page_title: pageName,
        page_location: window.location.href,
        page_path: window.location.pathname,
        custom_category: category,
      });
    }
  }, [pageName, category]);

  return null; // This component doesn't render anything
}
