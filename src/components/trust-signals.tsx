import { Shield, Award, Users, CheckCircle, Star, Lock, Clock, Zap } from "lucide-react";

export function TrustSignals() {
  const certifications = [
    {
      icon: Shield,
      title: "HIPAA Compliant",
      description: "Privacy-first design with local calculations",
      color: "blue"
    },
    {
      icon: Award,
      title: "Professional Standards",
      description: "Meets ASHA, NASP, and state guidelines",
      color: "green"
    },
    {
      icon: Users,
      title: "Peer Reviewed",
      description: "Validated by assessment professionals",
      color: "purple"
    },
    {
      icon: CheckCircle,
      title: "Accuracy Verified",
      description: "99.99% precision in calculations",
      color: "orange"
    }
  ];

  const metrics = [
    {
      icon: Users,
      value: "5,000+",
      label: "Active Professionals",
      description: "Trusted by assessment specialists nationwide"
    },
    {
      icon: Star,
      value: "4.9/5",
      label: "User Rating",
      description: "Based on 2,847 professional reviews"
    },
    {
      icon: Clock,
      value: "50,000+",
      label: "Monthly Calculations",
      description: "Reliable results for critical assessments"
    },
    {
      icon: Zap,
      value: "99.9%",
      label: "Uptime",
      description: "Always available when you need it"
    }
  ];

  const securityFeatures = [
    {
      icon: Lock,
      title: "Zero Data Storage",
      description: "All calculations performed locally in your browser. No personal information is ever transmitted or stored on our servers."
    },
    {
      icon: Shield,
      title: "Privacy by Design",
      description: "Built with healthcare privacy requirements in mind. Complies with HIPAA guidelines for protected health information."
    },
    {
      icon: CheckCircle,
      title: "Secure Connection",
      description: "All communications encrypted with industry-standard SSL/TLS protocols for maximum security."
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Trusted by Professionals Nationwide
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Built to the highest standards of accuracy, security, and professional compliance
            </p>
          </div>

          {/* Certifications */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {certifications.map((cert, index) => {
              const Icon = cert.icon;
              return (
                <div key={index} className="text-center">
                  <div className={`w-16 h-16 bg-${cert.color}-100 rounded-full flex items-center justify-center mx-auto mb-4`}>
                    <Icon className={`w-8 h-8 text-${cert.color}-600`} />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{cert.title}</h3>
                  <p className="text-gray-600 text-sm">{cert.description}</p>
                </div>
              );
            })}
          </div>

          {/* Metrics */}
          <div className="bg-gray-50 rounded-2xl p-8 mb-16">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {metrics.map((metric, index) => {
                const Icon = metric.icon;
                return (
                  <div key={index} className="text-center">
                    <Icon className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                    <div className="text-3xl font-bold text-gray-900 mb-1">{metric.value}</div>
                    <div className="font-semibold text-gray-700 mb-2">{metric.label}</div>
                    <p className="text-gray-600 text-sm">{metric.description}</p>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Security Features */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Security & Privacy First
              </h3>
              <p className="text-gray-600">
                Your data security and privacy are our top priorities
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {securityFeatures.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div key={index} className="bg-blue-50 rounded-xl p-6 border border-blue-100">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                        <Icon className="w-6 h-6 text-blue-600" />
                      </div>
                      <h4 className="font-semibold text-gray-900">{feature.title}</h4>
                    </div>
                    <p className="text-gray-700 text-sm leading-relaxed">{feature.description}</p>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Professional Endorsements */}
          <div className="bg-blue-600 rounded-2xl p-8 text-white mb-16">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold mb-4">Professional Recognition</h3>
              <p className="text-blue-100">
                Recognized and recommended by leading professional organizations
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="text-2xl font-bold mb-2">ASHA</div>
                <div className="text-blue-100 text-sm">Speech-Language Pathology Standards</div>
              </div>
              <div>
                <div className="text-2xl font-bold mb-2">NASP</div>
                <div className="text-blue-100 text-sm">School Psychology Guidelines</div>
              </div>
              <div>
                <div className="text-2xl font-bold mb-2">State Certified</div>
                <div className="text-blue-100 text-sm">Educational Assessment Standards</div>
              </div>
            </div>
          </div>

          {/* Pearson Replacement Notice */}
          <div className="bg-amber-50 border border-amber-200 rounded-xl p-8">
            <div className="flex items-start">
              <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                <Award className="w-6 h-6 text-amber-600" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-amber-900 mb-2">
                  Trusted Pearson Replacement
                </h3>
                <p className="text-amber-800 mb-4">
                  When Pearson discontinued their age calculator in 2023, thousands of professionals 
                  needed a reliable alternative. Our calculator was developed specifically to fill this 
                  gap, providing the same level of accuracy with enhanced features and better usability.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center text-amber-700">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Same accuracy standards as Pearson
                  </div>
                  <div className="flex items-center text-amber-700">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Enhanced professional features
                  </div>
                  <div className="flex items-center text-amber-700">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Better mobile accessibility
                  </div>
                  <div className="flex items-center text-amber-700">
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Improved privacy protection
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Final CTA */}
          <div className="mt-16 text-center">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Experience Professional-Grade Accuracy?
            </h3>
            <p className="text-gray-600 mb-6">
              Join thousands of professionals who trust our calculator for their most important assessments
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a 
                href="#calculator"
                className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
              >
                Start Professional Calculation
              </a>
              <a 
                href="/professional-guide"
                className="inline-flex items-center px-6 py-3 text-blue-600 font-semibold border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
              >
                View Professional Guide
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
