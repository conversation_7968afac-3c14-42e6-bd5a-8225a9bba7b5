import { Calculator, FileText, Users, Zap } from "lucide-react";

export function KeywordContent() {
  return (
    <section className="py-8 md:py-12 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-2">
              Professional Chronological Age Calculator
            </h2>
            <p className="text-base text-gray-600 max-w-3xl mx-auto">
              The most accurate and comprehensive online chronological age calculator.
              Used by healthcare professionals, educators, and researchers worldwide.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg p-6 shadow-sm text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Calculator className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Free Calculator</h3>
              <p className="text-gray-600 text-sm">
                100% free chronological age calculator with no registration required
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <FileText className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Assessment Ready</h3>
              <p className="text-gray-600 text-sm">
                Perfect for Pearson assessments and professional testing
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Multi-Purpose</h3>
              <p className="text-gray-600 text-sm">
                Suitable for medical, educational, and research applications
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Instant Results</h3>
              <p className="text-gray-600 text-sm">
                Get precise age calculations in years, months, and days instantly
              </p>
            </div>
          </div>

          {/* Popular Search Terms */}
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">
              Popular Age Calculation Searches
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Calculate Chronological Age</h4>
                <p className="text-blue-700 text-sm">
                  Step-by-step guide to calculating exact age in years, months, and days
                </p>
              </div>
              
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">Pearson Age Calculator</h4>
                <p className="text-green-700 text-sm">
                  Professional tool for Pearson assessments and educational testing
                </p>
              </div>
              
              <div className="p-4 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-2">Age Calculator for Testing</h4>
                <p className="text-purple-700 text-sm">
                  Specialized calculator for psychological and educational assessments
                </p>
              </div>
              
              <div className="p-4 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-2">Free Age Calculator</h4>
                <p className="text-orange-700 text-sm">
                  No cost, no registration - just accurate age calculations
                </p>
              </div>
              
              <div className="p-4 bg-pink-50 rounded-lg">
                <h4 className="font-medium text-pink-900 mb-2">Online Age Calculator</h4>
                <p className="text-pink-700 text-sm">
                  Web-based tool accessible from any device, anywhere
                </p>
              </div>
              
              <div className="p-4 bg-indigo-50 rounded-lg">
                <h4 className="font-medium text-indigo-900 mb-2">Age in Months Calculator</h4>
                <p className="text-indigo-700 text-sm">
                  Calculate precise age including months and days for assessments
                </p>
              </div>
            </div>
          </div>

          {/* How to Use */}
          <div className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">
              How to Calculate Chronological Age
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">
                  1
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Enter Birth Date</h4>
                <p className="text-gray-600 text-sm">
                  Input the exact date of birth in the format MM/DD/YYYY
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">
                  2
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Set Calculation Date</h4>
                <p className="text-gray-600 text-sm">
                  Choose the date for which you want to calculate the age
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold">
                  3
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Get Precise Results</h4>
                <p className="text-gray-600 text-sm">
                  Receive exact age in years, months, and days instantly
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
