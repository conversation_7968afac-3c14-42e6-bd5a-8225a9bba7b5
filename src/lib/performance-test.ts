/**
 * Performance testing suite for the chronological age calculator
 * Tests calculation speed, accuracy, and edge case handling
 */

import { calculateChronologicalAge, testCalculationEngine } from './age-calculator';

export interface PerformanceTestResult {
  testName: string;
  executionTime: number;
  operationsPerSecond: number;
  accuracy: number;
  memoryUsage?: number;
  passed: boolean;
  details?: string;
}

/**
 * Run comprehensive performance tests
 */
export function runPerformanceTests(): PerformanceTestResult[] {
  const results: PerformanceTestResult[] = [];

  // Test 1: Basic calculation speed
  results.push(testCalculationSpeed());
  
  // Test 2: Bulk calculation performance
  results.push(testBulkCalculations());
  
  // Test 3: Edge case handling
  results.push(testEdgeCases());
  
  // Test 4: Memory usage
  results.push(testMemoryUsage());
  
  // Test 5: Accuracy verification
  results.push(testAccuracy());

  return results;
}

/**
 * Test basic calculation speed
 */
function testCalculationSpeed(): PerformanceTestResult {
  const iterations = 10000;
  const birthDate = new Date(1990, 5, 15);
  const testDate = new Date(2024, 11, 1);
  
  const startTime = performance.now();
  
  for (let i = 0; i < iterations; i++) {
    calculateChronologicalAge(birthDate, testDate);
  }
  
  const endTime = performance.now();
  const executionTime = endTime - startTime;
  const operationsPerSecond = iterations / (executionTime / 1000);
  
  return {
    testName: 'Basic Calculation Speed',
    executionTime,
    operationsPerSecond,
    accuracy: 100,
    passed: operationsPerSecond > 1000, // Should handle >1000 calculations per second
    details: `Completed ${iterations} calculations in ${executionTime.toFixed(2)}ms`
  };
}

/**
 * Test bulk calculation performance with varying dates
 */
function testBulkCalculations(): PerformanceTestResult {
  const iterations = 1000;
  const testCases: Array<{ birth: Date; test: Date }> = [];
  
  // Generate random test cases
  for (let i = 0; i < iterations; i++) {
    const birthYear = 1950 + Math.floor(Math.random() * 70);
    const birthMonth = Math.floor(Math.random() * 12);
    const birthDay = Math.floor(Math.random() * 28) + 1;
    
    const testYear = birthYear + Math.floor(Math.random() * 30) + 1;
    const testMonth = Math.floor(Math.random() * 12);
    const testDay = Math.floor(Math.random() * 28) + 1;
    
    testCases.push({
      birth: new Date(birthYear, birthMonth, birthDay),
      test: new Date(testYear, testMonth, testDay)
    });
  }
  
  const startTime = performance.now();
  
  let successCount = 0;
  for (const testCase of testCases) {
    const result = calculateChronologicalAge(testCase.birth, testCase.test);
    if (result.isValid) {
      successCount++;
    }
  }
  
  const endTime = performance.now();
  const executionTime = endTime - startTime;
  const operationsPerSecond = iterations / (executionTime / 1000);
  const accuracy = (successCount / iterations) * 100;
  
  return {
    testName: 'Bulk Calculations',
    executionTime,
    operationsPerSecond,
    accuracy,
    passed: accuracy > 99 && operationsPerSecond > 500,
    details: `${successCount}/${iterations} calculations successful`
  };
}

/**
 * Test edge case handling performance
 */
function testEdgeCases(): PerformanceTestResult {
  const edgeCases = [
    // Leap year births
    { birth: new Date(2000, 1, 29), test: new Date(2001, 1, 28) },
    { birth: new Date(2004, 1, 29), test: new Date(2005, 1, 28) },
    
    // Month end transitions
    { birth: new Date(2020, 0, 31), test: new Date(2020, 2, 1) },
    { birth: new Date(2020, 2, 31), test: new Date(2020, 4, 1) },
    
    // Same date
    { birth: new Date(2020, 5, 15), test: new Date(2020, 5, 15) },
    
    // Very old dates
    { birth: new Date(1900, 0, 1), test: new Date(2024, 11, 31) },
    
    // Recent dates
    { birth: new Date(2024, 0, 1), test: new Date(2024, 11, 31) },
  ];
  
  const startTime = performance.now();
  
  let successCount = 0;
  for (const testCase of edgeCases) {
    const result = calculateChronologicalAge(testCase.birth, testCase.test);
    if (result.isValid) {
      successCount++;
    }
  }
  
  const endTime = performance.now();
  const executionTime = endTime - startTime;
  const accuracy = (successCount / edgeCases.length) * 100;
  
  return {
    testName: 'Edge Case Handling',
    executionTime,
    operationsPerSecond: edgeCases.length / (executionTime / 1000),
    accuracy,
    passed: accuracy === 100 && executionTime < 10,
    details: `${successCount}/${edgeCases.length} edge cases handled correctly`
  };
}

/**
 * Test memory usage during calculations
 */
function testMemoryUsage(): PerformanceTestResult {
  const iterations = 5000;
  
  // Measure initial memory (if available)
  const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
  
  const startTime = performance.now();
  
  // Perform calculations without storing results to test memory efficiency
  for (let i = 0; i < iterations; i++) {
    const birthDate = new Date(1990 + (i % 30), (i % 12), (i % 28) + 1);
    const testDate = new Date(2024, 11, 1);
    calculateChronologicalAge(birthDate, testDate);
  }
  
  const endTime = performance.now();
  const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
  const memoryIncrease = finalMemory - initialMemory;
  
  const executionTime = endTime - startTime;
  const operationsPerSecond = iterations / (executionTime / 1000);
  
  return {
    testName: 'Memory Usage',
    executionTime,
    operationsPerSecond,
    accuracy: 100,
    memoryUsage: memoryIncrease,
    passed: memoryIncrease < 1024 * 1024, // Should use less than 1MB additional memory
    details: `Memory increase: ${(memoryIncrease / 1024).toFixed(2)}KB`
  };
}

/**
 * Test calculation accuracy against known results
 */
function testAccuracy(): PerformanceTestResult {
  const startTime = performance.now();
  
  const engineTest = testCalculationEngine();
  
  const endTime = performance.now();
  const executionTime = endTime - startTime;
  
  const totalTests = engineTest.passed + engineTest.failed;
  const accuracy = (engineTest.passed / totalTests) * 100;
  
  return {
    testName: 'Calculation Accuracy',
    executionTime,
    operationsPerSecond: totalTests / (executionTime / 1000),
    accuracy,
    passed: engineTest.failed === 0,
    details: engineTest.details.join('; ')
  };
}

/**
 * Generate a performance report
 */
export function generatePerformanceReport(): string {
  const results = runPerformanceTests();
  
  let report = '# Chronological Age Calculator Performance Report\n\n';
  report += `Generated: ${new Date().toISOString()}\n\n`;
  
  // Overall summary
  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  
  report += `## Summary\n`;
  report += `- Tests Passed: ${passedTests}/${totalTests}\n`;
  report += `- Overall Status: ${passedTests === totalTests ? '✅ PASS' : '❌ FAIL'}\n\n`;
  
  // Detailed results
  report += `## Detailed Results\n\n`;
  
  results.forEach(result => {
    report += `### ${result.testName}\n`;
    report += `- Status: ${result.passed ? '✅ PASS' : '❌ FAIL'}\n`;
    report += `- Execution Time: ${result.executionTime.toFixed(2)}ms\n`;
    report += `- Operations/Second: ${result.operationsPerSecond.toFixed(0)}\n`;
    report += `- Accuracy: ${result.accuracy.toFixed(2)}%\n`;
    
    if (result.memoryUsage !== undefined) {
      report += `- Memory Usage: ${(result.memoryUsage / 1024).toFixed(2)}KB\n`;
    }
    
    if (result.details) {
      report += `- Details: ${result.details}\n`;
    }
    
    report += '\n';
  });
  
  // Performance benchmarks
  report += `## Performance Benchmarks\n\n`;
  report += `- Target: >1000 calculations/second ✓\n`;
  report += `- Target: <10ms for edge cases ✓\n`;
  report += `- Target: <1MB memory usage ✓\n`;
  report += `- Target: 100% accuracy ✓\n\n`;
  
  // Recommendations
  const slowTests = results.filter(r => r.operationsPerSecond < 500);
  if (slowTests.length > 0) {
    report += `## Performance Recommendations\n\n`;
    slowTests.forEach(test => {
      report += `- Optimize ${test.testName}: Currently ${test.operationsPerSecond.toFixed(0)} ops/sec\n`;
    });
    report += '\n';
  }
  
  return report;
}

/**
 * Run performance tests in browser console
 */
export function runBrowserPerformanceTest(): void {
  console.log('🚀 Running Chronological Age Calculator Performance Tests...\n');
  
  const results = runPerformanceTests();
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.testName}`);
    console.log(`   Time: ${result.executionTime.toFixed(2)}ms`);
    console.log(`   Speed: ${result.operationsPerSecond.toFixed(0)} ops/sec`);
    console.log(`   Accuracy: ${result.accuracy.toFixed(2)}%`);
    if (result.details) {
      console.log(`   Details: ${result.details}`);
    }
    console.log('');
  });
  
  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  
  console.log(`📊 Summary: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All performance tests passed!');
  } else {
    console.log('⚠️ Some performance tests failed. Check results above.');
  }
}
