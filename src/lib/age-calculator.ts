/**
 * Professional-grade chronological age calculation engine
 * Handles all edge cases including leap years, varying month lengths, and date transitions
 * Designed for medical and educational assessment accuracy
 */

export interface AgeCalculationResult {
  years: number;
  months: number;
  days: number;
  totalDays: number;
  totalWeeks: number;
  totalHours: number;
  totalMinutes: number;
  isValid: boolean;
  errorMessage?: string;
}

export interface DateInput {
  year: number;
  month: number; // 1-12
  day: number;
}

/**
 * Calculate precise chronological age between two dates
 * @param birthDate Birth date
 * @param assessmentDate Assessment/test date
 * @returns Detailed age calculation result
 */
export function calculateChronologicalAge(
  birthDate: Date | string,
  assessmentDate: Date | string
): AgeCalculationResult {
  try {
    // Parse dates
    const birth = typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
    const assessment = typeof assessmentDate === 'string' ? new Date(assessmentDate) : assessmentDate;

    // Validate dates
    if (isNaN(birth.getTime()) || isNaN(assessment.getTime())) {
      return {
        years: 0,
        months: 0,
        days: 0,
        totalDays: 0,
        totalWeeks: 0,
        totalHours: 0,
        totalMinutes: 0,
        isValid: false,
        errorMessage: "Invalid date format"
      };
    }

    if (birth > assessment) {
      return {
        years: 0,
        months: 0,
        days: 0,
        totalDays: 0,
        totalWeeks: 0,
        totalHours: 0,
        totalMinutes: 0,
        isValid: false,
        errorMessage: "Birth date cannot be after assessment date"
      };
    }

    // Calculate age using precise date arithmetic
    const ageResult = calculatePreciseAge(birth, assessment);
    
    // Calculate total time units
    const totalMilliseconds = assessment.getTime() - birth.getTime();
    const totalDays = Math.floor(totalMilliseconds / (1000 * 60 * 60 * 24));
    const totalWeeks = Math.floor(totalDays / 7);
    const totalHours = Math.floor(totalMilliseconds / (1000 * 60 * 60));
    const totalMinutes = Math.floor(totalMilliseconds / (1000 * 60));

    return {
      years: ageResult.years,
      months: ageResult.months,
      days: ageResult.days,
      totalDays,
      totalWeeks,
      totalHours,
      totalMinutes,
      isValid: true
    };
  } catch (error) {
    return {
      years: 0,
      months: 0,
      days: 0,
      totalDays: 0,
      totalWeeks: 0,
      totalHours: 0,
      totalMinutes: 0,
      isValid: false,
      errorMessage: error instanceof Error ? error.message : "Calculation error"
    };
  }
}

/**
 * Calculate precise age handling all edge cases
 */
function calculatePreciseAge(birth: Date, assessment: Date): { years: number; months: number; days: number } {
  let years = assessment.getFullYear() - birth.getFullYear();
  let months = assessment.getMonth() - birth.getMonth();
  let days = assessment.getDate() - birth.getDate();

  // Handle negative days
  if (days < 0) {
    months--;
    // Get the number of days in the previous month
    const previousMonth = new Date(assessment.getFullYear(), assessment.getMonth(), 0);
    days += previousMonth.getDate();
  }

  // Handle negative months
  if (months < 0) {
    years--;
    months += 12;
  }

  return { years, months, days };
}

/**
 * Format age result for different professional contexts
 */
export function formatAgeForProfessional(
  result: AgeCalculationResult,
  format: 'standard' | 'detailed' | 'slp' | 'psychology' | 'assessment'
): string {
  if (!result.isValid) {
    return "Invalid calculation";
  }

  switch (format) {
    case 'standard':
      return `${result.years};${result.months};${result.days}`;
    
    case 'detailed':
      return `${result.years} years, ${result.months} months, ${result.days} days`;
    
    case 'slp':
      return `${result.years} years, ${result.months} months`;
    
    case 'psychology':
      return `${result.years} years, ${result.months} months, ${result.days} days`;
    
    case 'assessment':
      return `${result.years};${result.months};${result.days}`;
    
    default:
      return `${result.years} years, ${result.months} months, ${result.days} days`;
  }
}

/**
 * Validate if a date is reasonable for age calculation
 */
export function validateDateForAgeCalculation(date: Date): { isValid: boolean; errorMessage?: string } {
  const now = new Date();
  const minDate = new Date(1900, 0, 1); // Reasonable minimum birth date
  const maxDate = new Date(now.getFullYear() + 1, 11, 31); // Allow future dates for planning

  if (date < minDate) {
    return {
      isValid: false,
      errorMessage: "Date is too far in the past"
    };
  }

  if (date > maxDate) {
    return {
      isValid: false,
      errorMessage: "Date is too far in the future"
    };
  }

  return { isValid: true };
}

/**
 * Calculate corrected age for premature infants
 * @param birthDate Actual birth date
 * @param gestationalAge Gestational age at birth in weeks
 * @param assessmentDate Assessment date
 * @returns Corrected age calculation
 */
export function calculateCorrectedAge(
  birthDate: Date | string,
  gestationalAge: number,
  assessmentDate: Date | string
): AgeCalculationResult {
  const birth = typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
  const assessment = typeof assessmentDate === 'string' ? new Date(assessmentDate) : assessmentDate;

  // Calculate weeks premature (40 weeks is full term)
  const weeksPremature = Math.max(0, 40 - gestationalAge);
  
  // Adjust birth date by adding the premature weeks
  const correctedBirthDate = new Date(birth);
  correctedBirthDate.setDate(correctedBirthDate.getDate() + (weeksPremature * 7));

  return calculateChronologicalAge(correctedBirthDate, assessment);
}

/**
 * Get age calculation for specific assessment tools
 */
export function getAgeForAssessment(
  birthDate: Date | string,
  assessmentDate: Date | string,
  assessmentTool: string
): { age: AgeCalculationResult; format: string; notes?: string } {
  const age = calculateChronologicalAge(birthDate, assessmentDate);
  
  switch (assessmentTool.toLowerCase()) {
    case 'wisc-v':
    case 'wisc-5':
      return {
        age,
        format: formatAgeForProfessional(age, 'standard'),
        notes: 'Age range: 6;0 to 16;11. Use exact chronological age for subtest selection.'
      };
    
    case 'celf-5':
      return {
        age,
        format: formatAgeForProfessional(age, 'slp'),
        notes: 'Age range: 5;0 to 21;11. Consider language exposure for bilingual assessments.'
      };
    
    case 'ppvt-5':
      return {
        age,
        format: formatAgeForProfessional(age, 'slp'),
        notes: 'Age range: 2;6 to 90+. Single-word receptive vocabulary assessment.'
      };
    
    case 'gfta-3':
      return {
        age,
        format: formatAgeForProfessional(age, 'slp'),
        notes: 'Age range: 2;0 to 21;11. Articulation assessment.'
      };
    
    default:
      return {
        age,
        format: formatAgeForProfessional(age, 'detailed'),
        notes: 'Standard chronological age calculation.'
      };
  }
}

/**
 * Test the calculation engine with known edge cases
 */
export function testCalculationEngine(): { passed: number; failed: number; details: string[] } {
  const testCases = [
    {
      name: "Leap year birth",
      birth: new Date(2020, 1, 29), // Feb 29, 2020
      assessment: new Date(2021, 1, 28), // Feb 28, 2021
      expected: { years: 0, months: 11, days: 30 }
    },
    {
      name: "Month end to month start",
      birth: new Date(2020, 0, 31), // Jan 31, 2020
      assessment: new Date(2020, 2, 1), // Mar 1, 2020
      expected: { years: 0, months: 1, days: 1 }
    },
    {
      name: "Same date",
      birth: new Date(2020, 5, 15), // Jun 15, 2020
      assessment: new Date(2020, 5, 15), // Jun 15, 2020
      expected: { years: 0, months: 0, days: 0 }
    }
  ];

  let passed = 0;
  let failed = 0;
  const details: string[] = [];

  testCases.forEach(testCase => {
    const result = calculateChronologicalAge(testCase.birth, testCase.assessment);
    const matches = result.years === testCase.expected.years &&
                   result.months === testCase.expected.months &&
                   result.days === testCase.expected.days;
    
    if (matches) {
      passed++;
      details.push(`✓ ${testCase.name}: PASSED`);
    } else {
      failed++;
      details.push(`✗ ${testCase.name}: FAILED - Expected ${testCase.expected.years};${testCase.expected.months};${testCase.expected.days}, got ${result.years};${result.months};${result.days}`);
    }
  });

  return { passed, failed, details };
}
