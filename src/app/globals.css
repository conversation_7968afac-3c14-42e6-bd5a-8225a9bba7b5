@import "tailwindcss";

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

/* Mobile Optimization for Professional Calculator */
@layer utilities {
  /* Touch-friendly buttons */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved mobile form inputs */
  .mobile-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
  }

  /* Mobile-optimized calculator interface */
  @media (max-width: 768px) {
    .calculator-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .mode-selector {
      grid-template-columns: 1fr 1fr;
      gap: 0.75rem;
    }

    .result-display {
      font-size: 1.5rem;
      line-height: 1.4;
    }

    /* Ensure proper spacing on mobile */
    .mobile-section {
      padding: 1rem;
    }

    /* Optimize button sizes for touch */
    .mobile-button {
      padding: 0.875rem 1.5rem;
      font-size: 1rem;
    }
  }

  /* Small mobile devices */
  @media (max-width: 480px) {
    .mode-selector {
      grid-template-columns: 1fr;
    }

    .result-display {
      font-size: 1.25rem;
    }

    .mobile-section {
      padding: 0.75rem;
    }
  }

  /* Landscape mobile optimization */
  @media (max-height: 500px) and (orientation: landscape) {
    .hero-section {
      padding-top: 2rem;
      padding-bottom: 2rem;
    }

    .section-spacing {
      padding-top: 3rem;
      padding-bottom: 3rem;
    }
  }

  /* Flexible date input that supports both typing and date picker */
  .date-input-flexible {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    color-scheme: light;
  }

  /* Keep the calendar picker visible and functional */
  .date-input-flexible::-webkit-calendar-picker-indicator {
    opacity: 0.7;
    cursor: pointer;
    margin-left: 4px;
  }

  .date-input-flexible::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
  }

  /* Ensure text input works properly */
  .date-input-flexible::-webkit-datetime-edit {
    font-family: inherit;
  }

  .date-input-flexible::-webkit-datetime-edit-fields-wrapper {
    font-family: inherit;
  }

  .date-input-flexible::-webkit-datetime-edit-text {
    font-family: inherit;
  }

  .date-input-flexible::-webkit-datetime-edit-month-field {
    font-family: inherit;
  }

  .date-input-flexible::-webkit-datetime-edit-day-field {
    font-family: inherit;
  }

  .date-input-flexible::-webkit-datetime-edit-year-field {
    font-family: inherit;
  }

  /* Ensure consistent styling across browsers */
  .date-input-flexible:lang(en) {
    font-family: inherit;
  }

  /* Firefox specific styling */
  .date-input-flexible::-moz-placeholder {
    opacity: 0.6;
  }
}