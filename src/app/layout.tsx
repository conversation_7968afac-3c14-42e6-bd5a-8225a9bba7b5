import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { cn } from "@/lib/utils";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { StructuredData } from "@/components/structured-data";
import { GoogleAnalytics } from "@/components/google-analytics";

const inter = Inter({ subsets: ["latin"], variable: "--font-sans" });
const montserrat = Montserrat({
  subsets: ["latin"],
  variable: "--font-montserrat",
});

export const metadata: Metadata = {
  title: "Professional Age Calculator - Pearson Alternative",
  description: "Medical-grade chronological age calculator for assessment professionals. Trusted Pearson alternative with Y;M;D format support.",
  keywords: "chronological age calculator, pearson age calculator alternative, professional age calculator, SLP age calculation, school psychology assessment, WISC-V age calculator, CELF-5 chronological age, assessment age calculation, speech therapy age calculator, psychoeducational evaluation age",
  authors: [{ name: "Professional Assessment Team" }],
  creator: "Chronological Age Calculator",
  publisher: "Chronological Age Calculator",
  alternates: {
    canonical: "https://chronological-age-calculator.org",
  },
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://chronological-age-calculator.org",
    title: "Professional Chronological Age Calculator - Trusted by Assessment Professionals",
    description: "Medical-grade accuracy for Speech Therapists, School Psychologists & Assessment Professionals. Reliable Pearson alternative with specialized professional modes and Y;M;D format support.",
    siteName: "Chronological Age Calculator",
  },
  twitter: {
    card: "summary_large_image",
    title: "Professional Chronological Age Calculator - Pearson Alternative",
    description: "Trusted by SLPs, School Psychologists & Assessment Professionals. Medical-grade accuracy with professional formats.",
    creator: "@chronoagecalc",
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased",
          inter.variable,
          montserrat.variable
        )}
      >
        <GoogleAnalytics />
        <StructuredData />
        <div className="relative flex min-h-screen flex-col">
          <Header />
          <main className="flex-1">{children}</main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
