import { Metadata } from 'next';
import { PearsonAlternativeHero } from '@/components/pearson-alternative/hero';
import { PearsonImpact } from '@/components/pearson-alternative/pearson-impact';
import { FeatureComparison } from '@/components/pearson-alternative/feature-comparison';
import { MigrationGuide } from '@/components/pearson-alternative/migration-guide';
import { UserTestimonials } from '@/components/pearson-alternative/user-testimonials';
import { TechnicalSpecs } from '@/components/pearson-alternative/technical-specs';
import { SupportCommitment } from '@/components/pearson-alternative/support-commitment';

export const metadata: Metadata = {
  title: "Pearson Age Calculator Alternative - Professional Replacement Solution",
  description: "Trusted alternative to <PERSON>'s discontinued chronological age calculator. Medical-grade accuracy, professional features, and seamless migration for assessment professionals.",
  keywords: "Pearson age calculator alternative, Pearson replacement, professional age calculator, assessment age calculation, Pearson discontinued calculator, chronological age calculator professional",
  openGraph: {
    title: "Professional Pearson Age Calculator Alternative",
    description: "Trusted replacement for <PERSON>'s discontinued age calculator. Medical-grade accuracy with enhanced professional features for assessment specialists.",
    url: "https://chronological-age-calculator.org/pearson-alternative",
  },
};

export default function PearsonAlternativePage() {
  return (
    <main>
      <PearsonAlternativeHero />
      <PearsonImpact />
      <FeatureComparison />
      <MigrationGuide />
      <UserTestimonials />
      <TechnicalSpecs />
      <SupportCommitment />
    </main>
  );
}
