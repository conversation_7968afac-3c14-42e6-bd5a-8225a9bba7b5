import { Metadata } from "next";
import { Clock, Target, Users, Award } from "lucide-react";

export const metadata: Metadata = {
  title: "About ChronoAge - Professional Chronological Age Calculator",
  description: "Learn about ChronoAge, the professional-grade chronological age calculator trusted by healthcare professionals, educators, and individuals worldwide.",
  keywords: "about chronoage, age calculator team, professional age calculation, medical age tools",
};

export default function AboutPage() {
  return (
    <main>
      {/* Hero Section */}
      <section className="py-16 md:py-24 bg-gradient-to-br from-blue-50 via-white to-blue-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              About ChronoAge
            </h1>
            <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
              We&apos;re dedicated to providing the most accurate and reliable chronological age 
              calculation tools for professionals and individuals worldwide.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                  Our Mission
                </h2>
                <p className="text-gray-700 mb-4">
                  ChronoAge was created to bridge the gap between complex age calculation 
                  requirements in professional settings and the need for accessible, 
                  accurate tools for everyone.
                </p>
                <p className="text-gray-700 mb-4">
                  We understand that precise age calculation is crucial in healthcare, 
                  education, and legal contexts, where even small discrepancies can 
                  impact important decisions.
                </p>
                <p className="text-gray-700">
                  Our goal is to provide professional-grade accuracy in a user-friendly 
                  format that serves both experts and individuals seeking reliable age calculations.
                </p>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center p-6 bg-blue-50 rounded-lg">
                  <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Precision</h3>
                  <p className="text-sm text-gray-600">
                    Professional-grade accuracy for critical applications
                  </p>
                </div>
                <div className="text-center p-6 bg-blue-50 rounded-lg">
                  <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Accessibility</h3>
                  <p className="text-sm text-gray-600">
                    Easy-to-use tools for everyone
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Why Professionals Trust ChronoAge
            </h2>
            <p className="text-lg text-gray-600">
              Our commitment to accuracy and reliability has made us the preferred choice 
              for professionals across multiple industries.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg p-8 shadow-sm">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Award className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
                Medical Standards
              </h3>
              <p className="text-gray-700 text-center">
                Our calculations meet the precision standards required by healthcare 
                institutions for pediatric assessments and medical research.
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-8 shadow-sm">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Clock className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
                Educational Compliance
              </h3>
              <p className="text-gray-700 text-center">
                Trusted by educational institutions for accurate age calculations 
                in standardized testing and academic assessments.
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-8 shadow-sm">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Users className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
                Privacy First
              </h3>
              <p className="text-gray-700 text-center">
                All calculations are performed locally in your browser. We never 
                store or transmit any personal information.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Technical Excellence */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-8 text-center">
              Technical Excellence
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Algorithm Accuracy
                </h3>
                <ul className="text-gray-700 space-y-2">
                  <li>• Handles all leap year calculations correctly</li>
                  <li>• Accounts for varying month lengths</li>
                  <li>• Manages edge cases and date boundaries</li>
                  <li>• Follows ISO 8601 international standards</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Quality Assurance
                </h3>
                <ul className="text-gray-700 space-y-2">
                  <li>• Extensive testing across date ranges</li>
                  <li>• Validation against medical standards</li>
                  <li>• Cross-platform compatibility testing</li>
                  <li>• Regular accuracy verification</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-16 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Questions About Our Tools?
            </h2>
            <p className="text-blue-100 mb-8 text-lg">
              We&apos;re here to help you understand how our age calculation tools 
              can meet your specific needs.
            </p>
            <a 
              href="/contact" 
              className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition"
            >
              Get in Touch
            </a>
          </div>
        </div>
      </section>
    </main>
  );
}
