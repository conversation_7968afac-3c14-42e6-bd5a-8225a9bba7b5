import { Metadata } from 'next';
import { TechnicalHero } from '@/components/technical/hero';
import { CalculationMethods } from '@/components/technical/calculation-methods';
import { AccuracyStandards } from '@/components/technical/accuracy-standards';
import { EdgeCaseHandling } from '@/components/technical/edge-case-handling';
import { ValidationTesting } from '@/components/technical/validation-testing';
import { TechnicalComparison } from '@/components/technical/technical-comparison';

export const metadata: Metadata = {
  title: "Calculation Methods & Accuracy - Technical Documentation",
  description: "Comprehensive technical documentation of our chronological age calculation methods, accuracy standards, and validation testing. Medical-grade precision for professional assessments.",
  keywords: "chronological age calculation methods, age calculation accuracy, medical grade precision, assessment calculation standards, technical documentation, validation testing",
  openGraph: {
    title: "Technical Documentation - Calculation Methods & Accuracy",
    description: "Medical-grade chronological age calculation methods with 99.99% accuracy. Complete technical documentation for assessment professionals.",
    url: "https://chronological-age-calculator.org/calculation-methods-and-accuracy",
  },
};

export default function CalculationMethodsPage() {
  return (
    <main>
      <TechnicalHero />
      <CalculationMethods />
      <AccuracyStandards />
      <EdgeCaseHandling />
      <ValidationTesting />
      <TechnicalComparison />
    </main>
  );
}
