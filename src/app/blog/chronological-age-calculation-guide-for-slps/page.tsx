import { Metadata } from 'next';
import { Calendar, Users, CheckCircle, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: "Complete Guide to Chronological Age Calculations for Speech-Language Pathologists",
  description: "Essential guide for SLPs on accurate chronological age calculations for CELF-5, PPVT-5, and other assessments. Learn professional standards and best practices.",
  keywords: "SLP age calculation, CELF-5 age requirements, PPVT-5 chronological age, speech therapy assessment age, SLP professional standards",
};

export default function SLPGuidePost() {
  return (
    <main className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-6">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Calculator
            </Link>
            
            <div className="flex items-center text-sm text-gray-600 mb-4">
              <Calendar className="w-4 h-4 mr-2" />
              Published: December 2024
              <span className="mx-2">•</span>
              <Users className="w-4 h-4 mr-2" />
              For Speech-Language Pathologists
            </div>
            
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
              Complete Guide to Chronological Age Calculations for Speech-Language Pathologists
            </h1>
            
            <p className="text-lg text-gray-600 leading-relaxed">
              As a Speech-Language Pathologist, accurate chronological age calculations are crucial for valid 
              assessment results. This comprehensive guide covers everything you need to know about age 
              calculations for CELF-5, PPVT-5, and other standardized assessments.
            </p>
          </div>

          {/* Content */}
          <article className="prose prose-lg max-w-none">
            <h2>Why Chronological Age Matters in SLP Assessments</h2>
            
            <p>
              Chronological age is the foundation of norm-referenced assessment in speech-language pathology. 
              Every standardized test relies on precise age calculations to:
            </p>
            
            <ul>
              <li>Select appropriate norm groups for score comparison</li>
              <li>Determine test starting points and ceiling rules</li>
              <li>Ensure valid interpretation of standard scores</li>
              <li>Meet professional standards for assessment accuracy</li>
            </ul>

            <h2>Essential Age Calculation Standards for SLPs</h2>
            
            <h3>CELF-5 Age Requirements</h3>
            <p>
              The Clinical Evaluation of Language Fundamentals - Fifth Edition requires precise chronological 
              age in Years;Months (Y;M) format. Key considerations:
            </p>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 my-6">
              <h4 className="text-green-900 font-semibold mb-3">CELF-5 Quick Reference</h4>
              <ul className="text-green-800 space-y-2">
                <li><strong>Age Range:</strong> 5;0 to 21;11</li>
                <li><strong>Format:</strong> Y;M (e.g., 6;8 for 6 years, 8 months)</li>
                <li><strong>Norm Groups:</strong> 6-month intervals</li>
                <li><strong>Critical:</strong> Use exact chronological age, not developmental age</li>
              </ul>
            </div>

            <h3>PPVT-5 Considerations</h3>
            <p>
              The Peabody Picture Vocabulary Test - Fifth Edition has specific age calculation requirements:
            </p>
            
            <ul>
              <li><strong>Age Range:</strong> 2;6 to 90+ years</li>
              <li><strong>Starting Points:</strong> Determined by chronological age</li>
              <li><strong>Basal/Ceiling:</strong> Age affects administration rules</li>
              <li><strong>Bilingual Considerations:</strong> Document both chronological age and language exposure</li>
            </ul>

            <h2>Step-by-Step Calculation Process</h2>
            
            <h3>Step 1: Verify Birth Date</h3>
            <p>
              Always confirm the birth date from official records. Never rely solely on parent/caregiver memory. 
              Acceptable sources include:
            </p>
            
            <ul>
              <li>Birth certificate</li>
              <li>Medical records</li>
              <li>School enrollment documents</li>
              <li>Previous assessment reports (if recent)</li>
            </ul>

            <h3>Step 2: Record Assessment Date</h3>
            <p>
              Use the actual date of test administration, not the report writing date. For multi-session 
              assessments, use the date of each specific subtest.
            </p>

            <h3>Step 3: Calculate Chronological Age</h3>
            <p>
              Use our professional calculator or follow this manual process:
            </p>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 my-6">
              <h4 className="text-blue-900 font-semibold mb-3">Manual Calculation Example</h4>
              <div className="text-blue-800 font-mono text-sm">
                <p>Birth Date: March 15, 2018</p>
                <p>Assessment Date: November 8, 2024</p>
                <p></p>
                <p>Years: 2024 - 2018 = 6 years</p>
                <p>Months: November (11) - March (3) = 8 months</p>
                <p>Days: 8 - 15 = -7 days (borrow from months)</p>
                <p></p>
                <p><strong>Result: 6 years, 7 months, 24 days (6;7)</strong></p>
              </div>
            </div>

            <h2>Common Mistakes to Avoid</h2>
            
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 my-6">
              <h4 className="text-red-900 font-semibold mb-3">⚠️ Critical Errors</h4>
              <ul className="text-red-800 space-y-2">
                <li><strong>Rounding Ages:</strong> Never round to the nearest month or year</li>
                <li><strong>Using Report Date:</strong> Always use actual assessment date</li>
                <li><strong>Ignoring Leap Years:</strong> February 29 births require special handling</li>
                <li><strong>Inconsistent Formats:</strong> Stick to Y;M format for consistency</li>
              </ul>
            </div>

            <h2>Special Considerations for SLPs</h2>
            
            <h3>Bilingual Assessments</h3>
            <p>
              When assessing bilingual clients, document both chronological age and language exposure:
            </p>
            
            <ul>
              <li>Chronological age: Used for norm comparison</li>
              <li>English exposure: Consider for interpretation</li>
              <li>Cultural factors: May affect age-appropriate expectations</li>
            </ul>

            <h3>Early Intervention Considerations</h3>
            <p>
              For children with premature birth history:
            </p>
            
            <ul>
              <li>Use chronological age for standardized assessments</li>
              <li>Document both chronological and corrected age</li>
              <li>Consider developmental context in interpretation</li>
              <li>Follow state-specific EI guidelines</li>
            </ul>

            <h2>Professional Documentation Standards</h2>
            
            <p>
              Your assessment reports should include:
            </p>
            
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 my-6">
              <h4 className="font-semibold mb-3">Documentation Checklist</h4>
              <div className="space-y-2">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                  <span>Birth date clearly stated</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                  <span>Assessment date documented</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                  <span>Chronological age in Y;M format</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                  <span>Norm group selection explained</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                  <span>Any special considerations noted</span>
                </div>
              </div>
            </div>

            <h2>Using Professional Calculation Tools</h2>
            
            <p>
              While manual calculations are important to understand, professional-grade calculators ensure 
              accuracy and save time. Our chronological age calculator offers:
            </p>
            
            <ul>
              <li>SLP-specific mode with assessment tool integration</li>
              <li>Automatic Y;M formatting</li>
              <li>Edge case handling (leap years, month variations)</li>
              <li>Mobile-optimized for field assessments</li>
            </ul>

            <h2>Conclusion</h2>
            
            <p>
              Accurate chronological age calculations are fundamental to valid SLP assessments. By following 
              these professional standards and using reliable calculation methods, you ensure that your 
              assessment results are accurate, defensible, and meet the highest professional standards.
            </p>
            
            <p>
              Remember: when in doubt, double-check your calculations. The validity of your entire assessment 
              depends on this crucial first step.
            </p>
          </article>

          {/* CTA */}
          <div className="mt-12 bg-blue-50 rounded-xl p-8 border border-blue-200 text-center">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Ready to Calculate Ages with Professional Accuracy?
            </h3>
            <p className="text-gray-600 mb-6">
              Use our SLP-optimized chronological age calculator for accurate, reliable results.
            </p>
            <Link 
              href="/#calculator"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
            >
              Open SLP Calculator Mode
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}
