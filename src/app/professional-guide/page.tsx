import { Metadata } from 'next';
import { ProfessionalGuideHero } from '@/components/professional-guide/hero';
import { SLPGuide } from '@/components/professional-guide/slp-guide';
import { SchoolPsychologyGuide } from '@/components/professional-guide/school-psychology-guide';
import { AssessmentStandards } from '@/components/professional-guide/assessment-standards';
import { ReportWritingGuide } from '@/components/professional-guide/report-writing-guide';
import { BestPractices } from '@/components/professional-guide/best-practices';
import { ProfessionalResources } from '@/components/professional-guide/professional-resources';

export const metadata: Metadata = {
  title: "Professional Guide - Chronological Age Calculator for Assessments",
  description: "Comprehensive guide for Speech Therapists, School Psychologists, and Assessment Professionals. Learn best practices for chronological age calculations in professional evaluations.",
  keywords: "professional age calculator guide, SLP assessment age calculation, school psychology chronological age, assessment standards, evaluation report writing, professional best practices",
  openGraph: {
    title: "Professional Assessment Guide - Chronological Age Calculator",
    description: "Expert guidance for Speech Therapists, School Psychologists & Assessment Professionals on accurate chronological age calculations.",
    url: "https://chronological-age-calculator.org/professional-guide",
  },
};

export default function ProfessionalGuidePage() {
  return (
    <main>
      <ProfessionalGuideHero />
      <SLPGuide />
      <SchoolPsychologyGuide />
      <AssessmentStandards />
      <ReportWritingGuide />
      <BestPractices />
      <ProfessionalResources />
    </main>
  );
}
