# 部署指南

## 域名配置
- 主域名：chronological-age-calculator.org
- 所有相关配置已更新为新域名

## 生产环境部署

### 1. Vercel 部署（推荐）
```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 部署
vercel --prod
```

### 2. 环境变量配置
在 Vercel 控制台中设置以下环境变量：
- `NEXT_PUBLIC_SITE_URL`: https://chronological-age-calculator.org
- `NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION`: 您的 Google 验证码

### 3. 域名设置
1. 在 Vercel 项目设置中添加自定义域名
2. 将 chronological-age-calculator.org 指向 Vercel
3. 配置 DNS 记录：
   - A 记录：@ -> 76.76.19.61
   - CNAME 记录：www -> cname.vercel-dns.com

### 4. 性能优化
- 启用 Vercel Analytics
- 配置 Edge Functions
- 启用图片优化

### 5. SEO 配置
- 提交 sitemap 到 Google Search Console
- 配置 Google Analytics（可选）
- 验证结构化数据

## 本地构建测试
```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 检查清单
- [ ] 域名配置正确
- [ ] 环境变量设置
- [ ] SSL 证书配置
- [ ] 性能测试通过
- [ ] SEO 配置验证
- [ ] 功能测试完成
